import sim,net,audio,modem,atcmd,utime,_thread,osTimer,dataCall
from machine import LCD, Pin
import lvgl as lv
from common import Abstract
import EventMesh
from misc import Power
from misc import USB
import net,network,gc
import fota

# Power.camVDD2V8Enable(1)
class St7567Manage():
    """
    st7567 inital
    """
    def __init__(self, Interface, SPICS, SPIRST, SPIDC, SPIPort=0, SPIMode=0, width=128, height=64, clk=0,
                 InitData=None):
        self._lcd_w = width
        self._lcd_h = height
        self._lcd_clk = clk
        self._interface = Interface
        self._spiport = SPIPort
        self._spimode = SPIMode
        self._spics = SPICS
        self._spidc = SPIDC
        self._spirst = SPIRST
        self.contrast_value = [
            23,
            24,
            25,
            26,
            28,
            32,
            34,
            36,
        ]

        init_para = (
            0, 0, 0xf8,
            0, 0, 0x01,
            0, 0, 0xA0,
            0, 0, 0xC0,
            0, 0, 0xA3,
            2, 0, 10,
            0, 0, 0x2c,
            2, 0, 10,
            0, 0, 0x2e,
            2, 0, 10,
            0, 0, 0x2f,
            2, 0, 10,
            0, 0, 0x25,
            0, 0, 0x81,
            0, 0, 0x35,
            0, 0, 0x60,
            0, 0, 0xC8,
            0, 0, 0xA0,
            0, 0, 0x81,
            0, 0, 26,
            0, 0, 0xaf
        )
        invalid = (
            0, 0, 0xB4)

        if InitData is None:
            self._initData = bytearray(init_para)
        else:
            self._initData = InitData

        self._invalidData = bytearray(invalid)
        self._lcd = LCD()
        self._lcd.lcd_init(self._initData,
                           self._lcd_w,
                           self._lcd_h,
                           self._lcd_clk,
                           1,
                           4,
                           1,
                           self._invalidData,
                           None,
                           None,
                           None,
                           self._interface,
                           self._spiport,
                           self._spimode,
                           self._spics,
                           self._spidc,
                           self._spirst)
    def _lcd_write(self):
        self._lcd.lcd_clear(0)
        self._lcd.lcd_write_cmd(0xF8,1)
        self._lcd.lcd_write_cmd(0x01,1)
        self._lcd.lcd_write_cmd(0x24,1)
        self._lcd.lcd_write_cmd(0x2F,1)
        self._lcd.lcd_write_cmd(0xA3,1)
        self._lcd.lcd_write_cmd(0x81,1)
        self._lcd.lcd_write_cmd(0x1A,1)
        self._lcd.lcd_write_cmd(0xA0,1)

        self._lcd.lcd_write_cmd(0xC8,1)
        self._lcd.lcd_write_cmd(0x40,1)
        self._lcd.lcd_write_cmd(0xA6,1)
        self._lcd.lcd_write_cmd(0xA4,1)
        self._lcd.lcd_write_cmd(0xB0,1)
        self._lcd.lcd_write_cmd(0xAF,1)

    def lcd_contrast(self,args):
        self._lcd.lcd_write_cmd(0x81,1)
        self._lcd.lcd_write_cmd(self.contrast_value[args],1)

    def _sleep(self):
        self._lcd.lcd_write_cmd(0xAE, 1)
        self._lcd.lcd_write_cmd(0xA5, 1)

    def _rouse(self):
        self._lcd.lcd_write_cmd(0xAE, 1)
        self._lcd.lcd_write_cmd(0xA5, 1)

    def lcd_reset(self):
        self._lcd.lcd_write_cmd(0xe2,1)
        self._lcd.lcd_write_cmd(0xc8,1)
        self._lcd.lcd_write_cmd(0x25,1)
        vol = EventMesh.publish("persistent_config_get", "contrast")
        if vol == None:
            vol = "5"
        self.lcd_contrast(int(vol)-1)
        
class LcdInitManage():
    
    lcd = St7567Manage(Interface=1, SPICS=11, SPIRST=19, SPIDC=43)
    Pin(Pin.GPIO38 , Pin.OUT, Pin.PULL_DISABLE, 1)
    LCD_gpio1 = Pin(Pin.GPIO3, Pin.OUT, Pin.PULL_DISABLE, 1)
    LCD_SIZE_W = 128
    LCD_SIZE_H = 64
    # 初始化lvgl
    lv.init()
    # Register SDL display driver.
    disp_buf1 = lv.disp_draw_buf_t()

    buf1_1 = bytearray(LCD_SIZE_W * LCD_SIZE_H * 2)
    disp_buf1.init(buf1_1, None, len(buf1_1))
    disp_drv = lv.disp_drv_t()
    disp_drv.init()
    
    disp_drv.draw_buf = disp_buf1
    disp_drv.flush_cb = lcd._lcd.lcd_write
    disp_drv.hor_res = LCD_SIZE_W
    disp_drv.ver_res = LCD_SIZE_H
    disp_drv.register()
    # LCD_gpio1.write(1)
    # 启动lvgv线程
    lv.tick_inc(5)
    @classmethod
    def get_lcd_obj(cls):
        """return lcd object"""
        return cls.lcd


lcd = LcdInitManage.get_lcd_obj()

language = "zh"
# 深色背景
dark_color_style_screen = lv.style_t()
dark_color_style_screen.init()
dark_color_style_screen.set_bg_color(lv.color_make(0xff, 0xff, 0xff))
dark_color_style_screen.set_bg_opa(255)
dark_color_style_screen.set_radius(0)
dark_color_style_screen.set_border_width(0)
# 浅色背景
light_color_style_screen = lv.style_t()
light_color_style_screen.init()
light_color_style_screen.set_bg_color(lv.color_make(0x00, 0x00, 0x00))
light_color_style_screen.set_bg_opa(255)
light_color_style_screen.set_radius(0)
light_color_style_screen.set_border_width(0)
# 黑色10字体
style_font_black_montserrat_10 = lv.style_t()
style_font_black_montserrat_10.init()
style_font_black_montserrat_10.set_radius(0)
style_font_black_montserrat_10.set_bg_color(lv.color_make(0x00, 0x00, 0x00))
style_font_black_montserrat_10.set_bg_grad_color(lv.color_make(0x00, 0x00, 0x00))
style_font_black_montserrat_10.set_bg_grad_dir(lv.GRAD_DIR.VER)
style_font_black_montserrat_10.set_bg_opa(0)
style_font_black_montserrat_10.set_border_width(0)
style_font_black_montserrat_10.set_text_color(lv.color_make(0xff, 0xff, 0xff))

style_font_black_montserrat_10.set_text_font(lv.font_09_songti)

style_font_black_montserrat_10.set_text_letter_space(0)
style_font_black_montserrat_10.set_pad_left(0)
style_font_black_montserrat_10.set_pad_right(0)
style_font_black_montserrat_10.set_pad_top(0)
style_font_black_montserrat_10.set_pad_bottom(0)
style_font_black_montserrat_10.set_anim_speed(5)#滚动速度调节
# 白色10字体
style_font_white_montserrat_10 = lv.style_t()
style_font_white_montserrat_10.init()
style_font_white_montserrat_10.set_radius(0)
style_font_white_montserrat_10.set_border_width(0)
style_font_white_montserrat_10.set_bg_color(lv.color_make(0xff, 0xff, 0xff))
style_font_white_montserrat_10.set_bg_grad_color(lv.color_make(0xff, 0xff, 0xff))
style_font_white_montserrat_10.set_bg_grad_dir(lv.GRAD_DIR.VER)
style_font_white_montserrat_10.set_bg_opa(0)
style_font_white_montserrat_10.set_text_color(lv.color_make(0x00, 0x00, 0x00))
style_font_white_montserrat_10.set_text_font(lv.font_09_songti)
style_font_white_montserrat_10.set_text_letter_space(0)
style_font_white_montserrat_10.set_pad_left(0)
style_font_white_montserrat_10.set_pad_right(0)
style_font_white_montserrat_10.set_pad_top(0)
style_font_white_montserrat_10.set_pad_bottom(0)
# 白色10小间距字体
style_font_white_montserrat_10_min_space = lv.style_t()
style_font_white_montserrat_10_min_space.init()
style_font_white_montserrat_10_min_space.set_radius(0)
style_font_white_montserrat_10_min_space.set_bg_color(lv.color_make(0xff, 0xff, 0xff))
style_font_white_montserrat_10_min_space.set_bg_grad_color(lv.color_make(0xff, 0xff, 0xff))
style_font_white_montserrat_10_min_space.set_bg_grad_dir(lv.GRAD_DIR.VER)
style_font_white_montserrat_10_min_space.set_bg_opa(0)
style_font_white_montserrat_10_min_space.set_text_color(lv.color_make(0x00, 0x00, 0x00))
style_font_white_montserrat_10_min_space.set_text_font(lv.font_09_songti)
style_font_white_montserrat_10_min_space.set_text_letter_space(-1)
style_font_white_montserrat_10_min_space.set_pad_left(0)
style_font_white_montserrat_10_min_space.set_pad_right(0)
style_font_white_montserrat_10_min_space.set_pad_top(0)
style_font_white_montserrat_10_min_space.set_pad_bottom(0)
# 黑色9H字体
style_font_black_montserrat_9h = lv.style_t()
style_font_black_montserrat_9h.init()
style_font_black_montserrat_9h.set_radius(0)
style_font_black_montserrat_9h.set_bg_color(lv.color_make(0x00, 0x00, 0x00))
style_font_black_montserrat_9h.set_bg_grad_color(lv.color_make(0x00, 0x00, 0x00))
style_font_black_montserrat_9h.set_bg_grad_dir(lv.GRAD_DIR.VER)
style_font_black_montserrat_9h.set_bg_opa(0)
style_font_black_montserrat_9h.set_text_color(lv.color_make(0xff, 0xff, 0xff))
style_font_black_montserrat_9h.set_text_font(lv.font_09_songti)
style_font_black_montserrat_9h.set_text_letter_space(-1)
style_font_black_montserrat_9h.set_pad_left(0)
style_font_black_montserrat_9h.set_pad_right(0)
style_font_black_montserrat_9h.set_pad_top(0)
style_font_black_montserrat_9h.set_pad_bottom(0)
style_font_black_montserrat_9h.set_anim_speed(5)#滚动速度调节

# 黑色9H字体
style_font_black_montserrat_9h_1 = lv.style_t()
style_font_black_montserrat_9h_1.init()
style_font_black_montserrat_9h_1.set_radius(0)
style_font_black_montserrat_9h_1.set_bg_color(lv.color_make(0x00, 0x00, 0x00))
style_font_black_montserrat_9h_1.set_bg_grad_color(lv.color_make(0x00, 0x00, 0x00))
style_font_black_montserrat_9h_1.set_bg_grad_dir(lv.GRAD_DIR.VER)
style_font_black_montserrat_9h_1.set_bg_opa(0)
style_font_black_montserrat_9h_1.set_text_color(lv.color_make(0xff, 0xff, 0xff))
style_font_black_montserrat_9h_1.set_text_font(lv.font_09_songti)
style_font_black_montserrat_9h_1.set_text_letter_space(-2)
style_font_black_montserrat_9h_1.set_pad_left(0)
style_font_black_montserrat_9h_1.set_pad_right(0)
style_font_black_montserrat_9h_1.set_pad_top(0)
style_font_black_montserrat_9h_1.set_pad_bottom(0)
style_font_black_montserrat_9h_1.set_anim_speed(5)#滚动速度调节
# 黑色18字体
style_font_black_montserrat_18 = lv.style_t()
style_font_black_montserrat_18.init()
style_font_black_montserrat_18.set_radius(0)
style_font_black_montserrat_18.set_bg_color(lv.color_make(0x00, 0x00, 0x00))
style_font_black_montserrat_18.set_bg_grad_color(lv.color_make(0x00, 0x00, 0x00))
style_font_black_montserrat_18.set_bg_grad_dir(lv.GRAD_DIR.VER)
style_font_black_montserrat_18.set_bg_opa(0)
style_font_black_montserrat_18.set_text_color(lv.color_make(0xff, 0xff, 0xff))
style_font_black_montserrat_18.set_text_font(lv.font_18_songti)
style_font_black_montserrat_18.set_text_letter_space(-1)  # 调整为紧凑间距以适应11位号码
style_font_black_montserrat_18.set_pad_left(0)
style_font_black_montserrat_18.set_pad_right(0)
style_font_black_montserrat_18.set_pad_top(0)
style_font_black_montserrat_18.set_pad_bottom(0)
# 黑色20字体
style_font_black_montserrat_20 = lv.style_t()
style_font_black_montserrat_20.init()
style_font_black_montserrat_20.set_radius(0)
style_font_black_montserrat_20.set_bg_color(lv.color_make(0x00, 0x00, 0x00))
style_font_black_montserrat_20.set_bg_grad_color(lv.color_make(0x00, 0x00, 0x00))
style_font_black_montserrat_20.set_bg_grad_dir(lv.GRAD_DIR.VER)
style_font_black_montserrat_20.set_bg_opa(0)
style_font_black_montserrat_20.set_text_color(lv.color_make(0xff, 0xff, 0xff))
style_font_black_montserrat_20.set_text_font(lv.font_20_songti)
style_font_black_montserrat_20.set_text_letter_space(0)
style_font_black_montserrat_20.set_pad_left(0)
style_font_black_montserrat_20.set_pad_right(0)
style_font_black_montserrat_20.set_pad_top(0)
style_font_black_montserrat_20.set_pad_bottom(0)
# 浅色列表背景
style_cont_black = lv.style_t()
style_cont_black.init()
style_cont_black.set_radius(0)
style_cont_black.set_bg_color(lv.color_make(0x00, 0x00, 0x00))
style_cont_black.set_bg_grad_color(lv.color_make(0x00, 0x00, 0x00))
style_cont_black.set_anim_speed(5)
style_cont_black.set_bg_grad_dir(lv.GRAD_DIR.VER)
style_cont_black.set_bg_opa(255)
style_cont_black.set_border_width(0)
style_cont_black.set_pad_left(0)
style_cont_black.set_pad_right(0)
style_cont_black.set_pad_top(0)
style_cont_black.set_pad_bottom(0)

# 列表样式
style_list_scrollbar = lv.style_t()
style_list_scrollbar.init()
style_list_scrollbar.set_radius(0)
style_list_scrollbar.set_bg_color(lv.color_make(0xff, 0xff, 0xff))
style_list_scrollbar.set_bg_opa(255)
# 列表样式 黑色 14
style_list_black = lv.style_t()
style_list_black.init()
style_list_black.set_radius(0)
style_list_black.set_bg_color(lv.color_make(0x00, 0x00, 0x00))
style_list_black.set_bg_grad_color(lv.color_make(0x00, 0x00, 0x00))
style_list_black.set_anim_speed(5)
style_list_black.set_bg_grad_dir(lv.GRAD_DIR.VER)
style_list_black.set_border_width(0)
style_list_black.set_bg_opa(255)
style_list_black.set_pad_left(0)
style_list_black.set_pad_right(0)
style_list_black.set_pad_top(0)
style_list_black.set_pad_bottom(0)
style_list_black.set_text_color(lv.color_make(0xff, 0xff, 0xff))
style_list_black.set_text_font(lv.font_09_songti)
# 深色列表背景
style_cont_white = lv.style_t()
style_cont_white.init()
style_cont_white.set_radius(2)
style_cont_white.set_bg_color(lv.color_make(0xff, 0xff, 0xff))
style_cont_white.set_bg_grad_color(lv.color_make(0xff, 0xff, 0xff))
style_cont_white.set_anim_speed(5)
style_cont_white.set_bg_grad_dir(lv.GRAD_DIR.VER)
style_cont_white.set_bg_opa(255)
style_cont_white.set_border_width(0)
style_cont_white.set_pad_left(0)
style_cont_white.set_pad_right(0)
style_cont_white.set_pad_top(0)
style_cont_white.set_pad_bottom(0)
# 列表样式 白色
style_list_white = lv.style_t()
style_list_white.init()
style_list_white.set_radius(2)
style_list_white.set_bg_color(lv.color_make(0xff, 0xff, 0xff))
style_list_white.set_bg_grad_color(lv.color_make(0xff, 0xff, 0xff))
style_list_white.set_anim_speed(5)
style_list_white.set_bg_grad_dir(lv.GRAD_DIR.VER)
style_list_white.set_border_width(0)
style_list_white.set_bg_opa(255)
style_list_white.set_pad_left(0)
style_list_white.set_pad_right(0)
style_list_white.set_pad_top(0)
style_list_white.set_pad_bottom(0)
style_list_white.set_text_color(lv.color_make(0x00, 0x00, 0x00))
# style_list_black.set_text_font(lv.font_14)
style_line = lv.style_t()
style_line.init()
style_line.set_line_color(lv.color_make(0xff, 0xff, 0xff))
style_line.set_line_width(1)
style_line.set_line_rounded(255)
####################################################################################
#                                   界面
####################################################################################
############################设置二级界面
setting_screen = lv.obj()
setting_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#话机设置
setting_list_title = lv.label(setting_screen)
setting_list_title.set_pos(0, 2)
setting_list_title.set_size(128, 48)
setting_list_title.set_text("话机设置")
setting_list_title.set_long_mode(lv.label.LONG.SCROLL_CIRCULAR)
setting_list_title.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
setting_list_title.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#话机设置列表
setting_list = lv.list(setting_screen)
setting_list.set_pos(0, 16)
setting_list.set_size(128, 16)
setting_list.set_style_pad_left(0, 0)
setting_list.set_style_pad_top(0, 1)
setting_list.set_style_pad_row(0, 0)
setting_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
setting_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
#设置页面确认按钮样式
setting_list_sure = lv.label(setting_screen)
setting_list_sure.set_pos(0, 51)
setting_list_sure.set_size(48, 12)
setting_list_sure.set_text("选择")
setting_list_sure.set_long_mode(lv.label.LONG.WRAP)
setting_list_sure.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
# add style for setting_list_sure
setting_list_sure.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#设置页面返回按钮样式
setting_list_return = lv.label(setting_screen)
setting_list_return.set_pos(103, 51)
setting_list_return.set_size(24, 12)
setting_list_return.set_text("返回")
setting_list_return.set_long_mode(lv.label.LONG.WRAP)
setting_list_return.set_style_text_align(lv.TEXT_ALIGN.RIGHT, 0)
setting_list_return.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)

Lan_setting_screen = lv.obj()
Lan_setting_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#lan设置
Lan_setting_list_title = lv.label(Lan_setting_screen)
Lan_setting_list_title.set_pos(0, 2)
Lan_setting_list_title.set_size(128, 48)
Lan_setting_list_title.set_text("Lan Setting")
Lan_setting_list_title.set_long_mode(lv.label.LONG.SCROLL_CIRCULAR)
Lan_setting_list_title.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
Lan_setting_list_title.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#lan设置列表
Lan_setting_list= lv.list(Lan_setting_screen)
Lan_setting_list.set_pos(0, 16)
Lan_setting_list.set_size(128, 16)
Lan_setting_list.set_style_pad_left(0, 0)
Lan_setting_list.set_style_pad_top(0, 1)
Lan_setting_list.set_style_pad_row(0, 0)
Lan_setting_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
Lan_setting_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
#lan设置页面确认按钮样式
Lan_setting_list_sure = lv.label(Lan_setting_screen)
Lan_setting_list_sure.set_pos(0, 51)
Lan_setting_list_sure.set_size(48, 12)
Lan_setting_list_sure.set_text("选择")
Lan_setting_list_sure.set_long_mode(lv.label.LONG.WRAP)
Lan_setting_list_sure.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
# add style for setting_list_sure
Lan_setting_list_sure.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#lan设置页面返回按钮样式
Lan_setting_list_return = lv.label(Lan_setting_screen)
Lan_setting_list_return.set_pos(103, 51)
Lan_setting_list_return.set_size(24, 12)
Lan_setting_list_return.set_text("返回")
Lan_setting_list_return.set_long_mode(lv.label.LONG.WRAP)
Lan_setting_list_return.set_style_text_align(lv.TEXT_ALIGN.RIGHT, 0)
Lan_setting_list_return.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)

#######lan开关界面
lan_switch_screen = lv.obj()
lan_switch_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#volte设置
lan_switch_list_title = lv.label(lan_switch_screen)
lan_switch_list_title.set_pos(0, 0)
lan_switch_list_title.set_size(128, 52)
lan_switch_list_title.set_text("Lan Switch")
lan_switch_list_title.set_long_mode(lv.label.LONG.WRAP)
lan_switch_list_title.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
lan_switch_list_title.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#volte设置列表
lan_switch_list = lv.list(lan_switch_screen)
lan_switch_list.set_pos(0, 16)
lan_switch_list.set_size(128, 16)
lan_switch_list.set_style_pad_left(0, 0)
lan_switch_list.set_style_pad_top(0, 1)
lan_switch_list.set_style_pad_row(0, 0)
lan_switch_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
lan_switch_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
#lan页面确认按钮样式
lan_switch_list_sure = lv.label(lan_switch_screen)
lan_switch_list_sure.set_pos(0, 51)
lan_switch_list_sure.set_size(24, 12)
lan_switch_list_sure.set_text("确定")
lan_switch_list_sure.set_long_mode(lv.label.LONG.WRAP)
lan_switch_list_sure.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
lan_switch_list_sure.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#volte页面返回按钮样式
lan_switch_list_return = lv.label(lan_switch_screen)
lan_switch_list_return.set_pos(103, 51)
lan_switch_list_return.set_size(24, 12)
lan_switch_list_return.set_text("返回")
lan_switch_list_return.set_long_mode(lv.label.LONG.WRAP)
lan_switch_list_return.set_style_text_align(lv.TEXT_ALIGN.RIGHT, 0)
lan_switch_list_return.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)

#显示热点开关
lan_switch_screen_value = lv.label(lan_switch_screen)
lan_switch_screen_value.set_pos(51, 51)
lan_switch_screen_value.set_size(48, 12)
lan_switch_screen_value.set_text("")
lan_switch_screen_value.set_long_mode(lv.label.LONG.WRAP)
lan_switch_screen_value.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
lan_switch_screen_value.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)


app_screen = lv.obj()
app_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#应用
app_list_title = lv.label(app_screen)
app_list_title.set_pos(0, 2)
app_list_title.set_size(128, 48)
app_list_title.set_text("话机应用")
app_list_title.set_long_mode(lv.label.LONG.SCROLL_CIRCULAR)
app_list_title.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
app_list_title.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#话机应用列表
app_list = lv.list(app_screen)
app_list.set_pos(0, 16)
app_list.set_size(128, 16)
app_list.set_style_pad_left(0, 0)
app_list.set_style_pad_top(0, 1)
app_list.set_style_pad_row(0, 0)
app_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
app_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
#应用页面确认按钮样式
app_list_sure = lv.label(app_screen)
app_list_sure.set_pos(0, 51)
app_list_sure.set_size(48, 12)
app_list_sure.set_text("选择")
app_list_sure.set_long_mode(lv.label.LONG.WRAP)
app_list_sure.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
# add style for setting_list_sure
app_list_sure.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#应用页面返回按钮样式
app_list_return = lv.label(app_screen)
app_list_return.set_pos(103, 51)
app_list_return.set_size(24, 12)
app_list_return.set_text("返回")
app_list_return.set_long_mode(lv.label.LONG.WRAP)
app_list_return.set_style_text_align(lv.TEXT_ALIGN.RIGHT, 0)
app_list_return.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)

########################################################################
# description: 闹钟列表
# author: pawn 
#########################################################################
AlarmClocklist_screen = lv.obj()
AlarmClocklist_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#
AlarmClocklist_list = lv.list(AlarmClocklist_screen)
AlarmClocklist_list.set_pos(0, 0)
AlarmClocklist_list.set_size(128, 48)
AlarmClocklist_list.set_style_pad_left(0, 0)
AlarmClocklist_list.set_style_pad_top(0, 1)
AlarmClocklist_list.set_style_pad_row(0, 0)
AlarmClocklist_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
AlarmClocklist_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
AlarmClocklist_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.SCROLLED)

# 底部功能栏
AlarmClocklist_options_label = lv.label(AlarmClocklist_screen)
AlarmClocklist_options_label.set_pos(0, 51)
AlarmClocklist_options_label.set_size(24, 12)
AlarmClocklist_options_label.set_text("选项")
AlarmClocklist_options_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
AlarmClocklist_options_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
AlarmClocklist_back_label = lv.label(AlarmClocklist_screen)
AlarmClocklist_back_label.set_pos(103, 51)
AlarmClocklist_back_label.set_size(24, 12)
AlarmClocklist_back_label.set_text("返回")
AlarmClocklist_back_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
AlarmClocklist_back_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT) 
#底部闹钟状态
AlarmClocklist_value = lv.label(AlarmClocklist_screen)
AlarmClocklist_value.set_pos(52, 51)
AlarmClocklist_value.set_size(24, 12)
AlarmClocklist_value.set_text("")
AlarmClocklist_value.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
AlarmClocklist_value.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
# 空列表显示界面
AlarmClocklist_NULL_screen = lv.obj()
AlarmClocklist_NULL_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)

AlarmClocklist_NULL_label = lv.label(AlarmClocklist_NULL_screen)
AlarmClocklist_NULL_label.set_pos(35, 25)
AlarmClocklist_NULL_label.set_size(60, 12)
AlarmClocklist_NULL_label.set_text("")
AlarmClocklist_NULL_label.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
AlarmClocklist_NULL_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)

# 底部功能栏
AlarmClocklist_NULL_options_label = lv.label(AlarmClocklist_NULL_screen)
AlarmClocklist_NULL_options_label.set_pos(0, 51)
AlarmClocklist_NULL_options_label.set_size(24, 12)
AlarmClocklist_NULL_options_label.set_text("选项")
AlarmClocklist_NULL_options_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
AlarmClocklist_NULL_options_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
AlarmClocklist_NULL_back_label = lv.label(AlarmClocklist_NULL_screen)
AlarmClocklist_NULL_back_label.set_pos(103, 51)
AlarmClocklist_NULL_back_label.set_size(24, 12)
AlarmClocklist_NULL_back_label.set_text("返回")
AlarmClocklist_NULL_back_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
AlarmClocklist_NULL_back_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
########################################################################
# description: 闹钟选项
# author: pawn 
#########################################################################
AlarmClockOption_screen = lv.obj()
AlarmClockOption_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#
AlarmClockOption_list = lv.list(AlarmClockOption_screen)
AlarmClockOption_list.set_pos(0, 0)
AlarmClockOption_list.set_size(128, 48)
AlarmClockOption_list.set_style_pad_left(0, 0)
AlarmClockOption_list.set_style_pad_top(0, 1)
AlarmClockOption_list.set_style_pad_row(0, 0)
AlarmClockOption_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
AlarmClockOption_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
AlarmClockOption_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.SCROLLED)
# 底部功能栏
AlarmClockOption_sure_label = lv.label(AlarmClockOption_screen)
AlarmClockOption_sure_label.set_pos(0, 51)
AlarmClockOption_sure_label.set_size(24, 12)
AlarmClockOption_sure_label.set_text("确定")
AlarmClockOption_sure_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
AlarmClockOption_sure_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
AlarmClockOption_back_label = lv.label(AlarmClockOption_screen)
AlarmClockOption_back_label.set_pos(103, 51)
AlarmClockOption_back_label.set_size(24, 12)
AlarmClockOption_back_label.set_text("返回")
AlarmClockOption_back_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
AlarmClockOption_back_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)

########################################################################
# description: 增加闹钟
# author: pawn 
#########################################################################
AddAlarmClock_screen = lv.obj()
AddAlarmClock_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#
AddAlarmClock_list_value = lv.label(AddAlarmClock_screen)
AddAlarmClock_list_value.set_pos(25, 51)
AddAlarmClock_list_value.set_size(75, 12)
AddAlarmClock_list_value.set_text("")
AddAlarmClock_list_value.set_long_mode(lv.label.LONG.SCROLL_CIRCULAR)
AddAlarmClock_list_value.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
AddAlarmClock_list_value.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)

AddAlarmClock_list = lv.list(AddAlarmClock_screen)
AddAlarmClock_list.set_pos(0, 0)
AddAlarmClock_list.set_size(128, 48)
AddAlarmClock_list.set_style_pad_left(0, 0)
AddAlarmClock_list.set_style_pad_top(0, 1)
AddAlarmClock_list.set_style_pad_row(0, 0)
AddAlarmClock_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
AddAlarmClock_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
AddAlarmClock_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.SCROLLED)
# 底部功能栏
AddAlarmClock_sure_label = lv.label(AddAlarmClock_screen)
AddAlarmClock_sure_label.set_pos(0, 51)
AddAlarmClock_sure_label.set_size(24, 12)
AddAlarmClock_sure_label.set_text("确定")
AddAlarmClock_sure_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
AddAlarmClock_sure_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)

AddAlarmClock_back_label = lv.label(AddAlarmClock_screen)
AddAlarmClock_back_label.set_pos(103, 51)
AddAlarmClock_back_label.set_size(24, 12)
AddAlarmClock_back_label.set_text("返回")
AddAlarmClock_back_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
AddAlarmClock_back_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)


########################################################################
# description: 重复方式
# author: pawn 
#########################################################################
Repeat_way_screen = lv.obj()
Repeat_way_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#
Repeat_way_list_value = lv.label(Repeat_way_screen)
Repeat_way_list_value.set_pos(25, 51)
Repeat_way_list_value.set_size(75, 12)
Repeat_way_list_value.set_text("")
Repeat_way_list_value.set_long_mode(lv.label.LONG.SCROLL_CIRCULAR)
Repeat_way_list_value.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
Repeat_way_list_value.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)

Repeat_way_list = lv.list(Repeat_way_screen)
Repeat_way_list.set_pos(0, 0)
Repeat_way_list.set_size(128, 48)
Repeat_way_list.set_style_pad_left(0, 0)
Repeat_way_list.set_style_pad_top(0, 1)
Repeat_way_list.set_style_pad_row(0, 0)
Repeat_way_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
Repeat_way_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
Repeat_way_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.SCROLLED)
# 底部功能栏
Repeat_way_sure_label = lv.label(Repeat_way_screen)
Repeat_way_sure_label.set_pos(0, 51)
Repeat_way_sure_label.set_size(24, 12)
Repeat_way_sure_label.set_text("确定")
Repeat_way_sure_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
Repeat_way_sure_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)

Repeat_way_back_label = lv.label(Repeat_way_screen)
Repeat_way_back_label.set_pos(103, 51)
Repeat_way_back_label.set_size(24, 12)
Repeat_way_back_label.set_text("返回")
Repeat_way_back_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
Repeat_way_back_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)


########################################################################
# description: 自定义
# author: pawn 
#########################################################################
AlarmClockcustomer_screen = lv.obj()
AlarmClockcustomer_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)

AlarmClockcustomer_list = lv.list(AlarmClockcustomer_screen)
AlarmClockcustomer_list.set_pos(0, 0)
AlarmClockcustomer_list.set_size(128, 48)
AlarmClockcustomer_list.set_style_pad_left(0, 0)
AlarmClockcustomer_list.set_style_pad_top(0, 1)
AlarmClockcustomer_list.set_style_pad_row(0, 0)
AlarmClockcustomer_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
AlarmClockcustomer_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
AlarmClockcustomer_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.SCROLLED)

# 底部功能栏
AlarmClockcustomer_sure_label = lv.label(AlarmClockcustomer_screen)
AlarmClockcustomer_sure_label.set_pos(0, 51)
AlarmClockcustomer_sure_label.set_size(24, 12)
AlarmClockcustomer_sure_label.set_text("确定")
AlarmClockcustomer_sure_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
AlarmClockcustomer_sure_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)

AlarmClockcustomer_back_label = lv.label(AlarmClockcustomer_screen)
AlarmClockcustomer_back_label.set_pos(103, 51)
AlarmClockcustomer_back_label.set_size(24, 12)
AlarmClockcustomer_back_label.set_text("返回")
AlarmClockcustomer_back_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
AlarmClockcustomer_back_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)


########################################################################
# description: 闹钟响铃界面
# author: pawn 
#########################################################################
AlarmClockring_screen = lv.obj()
AlarmClockring_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)

# 底部功能栏
AlarmClockring_close_label = lv.label(AlarmClockring_screen)
AlarmClockring_close_label.set_pos(0, 51)
AlarmClockring_close_label.set_size(36, 12)
AlarmClockring_close_label.set_text("关闭")
AlarmClockring_close_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
AlarmClockring_close_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)

AlarmClockring_delay_label = lv.label(AlarmClockring_screen)
AlarmClockring_delay_label.set_pos(103, 51)
AlarmClockring_delay_label.set_size(36, 12)
AlarmClockring_delay_label.set_text("延时")
AlarmClockring_delay_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
AlarmClockring_delay_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)

AlarmClockring_time_value = lv.label(AlarmClockring_screen)
AlarmClockring_time_value.set_pos(0, 18)
AlarmClockring_time_value.set_size(128, 12)
AlarmClockring_time_value.set_text("00:00")
AlarmClockring_time_value.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
AlarmClockring_time_value.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)

AlarmClockring_name_value = lv.label(AlarmClockring_screen)
AlarmClockring_name_value.set_pos(0, 32)
AlarmClockring_name_value.set_size(128, 12)
AlarmClockring_name_value.set_text("闹钟")
AlarmClockring_name_value.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
AlarmClockring_name_value.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)







############################音量设置列表页
sound_screen = lv.obj()
# add style for sound_screen
sound_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#音量设置标题
sound_setting_title = lv.label(sound_screen)
sound_setting_title.set_pos(0, 0)
sound_setting_title.set_size(128, 52)
sound_setting_title.set_text("声音")
sound_setting_title.set_long_mode(lv.label.LONG.WRAP)
sound_setting_title.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
sound_setting_title.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#音量列表样式
sound_setting_list = lv.list(sound_screen)
sound_setting_list.set_pos(0, 40)
sound_setting_list.set_size(128, 32)
sound_setting_list.set_style_pad_left(2, 0)
sound_setting_list.set_style_pad_top(4, 0)
sound_setting_list.set_style_pad_row(3, 0)
sound_setting_list.add_style(style_cont_white, lv.PART.MAIN | lv.STATE.DEFAULT)
sound_setting_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
sound_setting_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.SCROLLED)
#音量设置状态栏 选择
sound_setting_sure = lv.label(sound_screen)
sound_setting_sure.set_pos(0, 51)
sound_setting_sure.set_size(48, 12)
sound_setting_sure.set_text("选择")
sound_setting_sure.set_long_mode(lv.label.LONG.WRAP)
sound_setting_sure.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
sound_setting_sure.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#音量设置状态栏 返回
sound_setting_return = lv.label(sound_screen)
sound_setting_return.set_pos(103, 51)
sound_setting_return.set_size(24, 12)
sound_setting_return.set_text("返回")
sound_setting_return.set_long_mode(lv.label.LONG.WRAP)
sound_setting_return.set_style_text_align(lv.TEXT_ALIGN.RIGHT, 0)
sound_setting_return.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
############################音量设置界面
volume_screen = lv.obj()
# add style for sound_screen
volume_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#音量设置标题
volume_setting_title = lv.label(volume_screen)
volume_setting_title.set_pos(0, 0)
volume_setting_title.set_size(128, 52)
volume_setting_title.set_text("音量设置")
volume_setting_title.set_long_mode(lv.label.LONG.WRAP)
volume_setting_title.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
volume_setting_title.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#音量列表样式
volume_setting_list = lv.list(volume_screen)
volume_setting_list.set_pos(0, 40)
volume_setting_list.set_size(128, 32)
volume_setting_list.set_style_pad_left(2, 0)
volume_setting_list.set_style_pad_top(4, 0)
volume_setting_list.set_style_pad_row(3, 0)
volume_setting_list.add_style(style_cont_white, lv.PART.MAIN | lv.STATE.DEFAULT)
volume_setting_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
volume_setting_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.SCROLLED)
#音量设置状态栏 选择
volume_setting_sure = lv.label(volume_screen)
volume_setting_sure.set_pos(0, 51)
volume_setting_sure.set_size(48, 12)
volume_setting_sure.set_text("选择")
volume_setting_sure.set_long_mode(lv.label.LONG.WRAP)
volume_setting_sure.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
volume_setting_sure.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#音量设置的等级
volume_setting_value = lv.label(volume_screen)
volume_setting_value.set_pos(52, 51)
volume_setting_value.set_size(48, 12)
volume_setting_value.set_text("中等")
volume_setting_value.set_long_mode(lv.label.LONG.WRAP)
volume_setting_value.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
volume_setting_value.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#音量设置状态栏 返回
volume_setting_return = lv.label(volume_screen)
volume_setting_return.set_pos(103, 51)
volume_setting_return.set_size(24, 12)
volume_setting_return.set_text("返回")
volume_setting_return.set_long_mode(lv.label.LONG.WRAP)
volume_setting_return.set_style_text_align(lv.TEXT_ALIGN.RIGHT, 0)
volume_setting_return.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
############################音量选择界面
volume_select_screen = lv.obj()
# add style for sound_screen
volume_select_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#音量设置标题
volume_select_title = lv.label(volume_select_screen)
volume_select_title.set_pos(0, 0)
volume_select_title.set_size(128, 52)
volume_select_title.set_text("音量设置")
volume_select_title.set_long_mode(lv.label.LONG.WRAP)
volume_select_title.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
volume_select_title.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#音量列表样式
volume_select_list = lv.list(volume_select_screen)
volume_select_list.set_pos(0, 40)
volume_select_list.set_size(128, 32)
volume_select_list.set_style_pad_left(2, 0)
volume_select_list.set_style_pad_top(4, 0)
volume_select_list.set_style_pad_row(3, 0)
volume_select_list.add_style(style_cont_white, lv.PART.MAIN | lv.STATE.DEFAULT)
volume_select_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
volume_select_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.SCROLLED)
#音量设置状态栏 选择
volume_select_sure = lv.label(volume_select_screen)
volume_select_sure.set_pos(0, 51)
volume_select_sure.set_size(24, 12)
volume_select_sure.set_text("保存")
volume_select_sure.set_long_mode(lv.label.LONG.WRAP)
volume_select_sure.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
volume_select_sure.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#音量设置状态栏 返回
volume_select_return = lv.label(volume_select_screen)
volume_select_return.set_pos(103, 51)
volume_select_return.set_size(24, 12)
volume_select_return.set_text("返回")
volume_select_return.set_long_mode(lv.label.LONG.WRAP)
volume_select_return.set_style_text_align(lv.TEXT_ALIGN.RIGHT, 0)
volume_select_return.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
############################铃声设置界面
ring_screen = lv.obj()
# add style for sound_screen
ring_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#铃声设置标题
ring_setting_title = lv.label(ring_screen)
ring_setting_title.set_pos(0, 0)
ring_setting_title.set_size(128, 52)
ring_setting_title.set_text("铃声设置")
ring_setting_title.set_long_mode(lv.label.LONG.WRAP)
ring_setting_title.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
ring_setting_title.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#铃声列表样式
ring_setting_list = lv.list(ring_screen)
ring_setting_list.set_pos(0, 40)
ring_setting_list.set_size(128, 32)
ring_setting_list.set_style_pad_left(2, 0)
ring_setting_list.set_style_pad_top(4, 0)
ring_setting_list.set_style_pad_row(3, 0)
ring_setting_list.add_style(style_cont_white, lv.PART.MAIN | lv.STATE.DEFAULT)
ring_setting_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
ring_setting_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.SCROLLED)
#铃声设置状态栏 选择
ring_setting_sure = lv.label(ring_screen)
ring_setting_sure.set_pos(0, 51)
ring_setting_sure.set_size(40, 12)
ring_setting_sure.set_text("选择")
ring_setting_sure.set_long_mode(lv.label.LONG.WRAP)
ring_setting_sure.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
ring_setting_sure.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#铃声设置状态栏 返回
ring_setting_return = lv.label(ring_screen)
ring_setting_return.set_pos(103, 51)
ring_setting_return.set_size(24, 12)
ring_setting_return.set_text("返回")
ring_setting_return.set_long_mode(lv.label.LONG.WRAP)
ring_setting_return.set_style_text_align(lv.TEXT_ALIGN.RIGHT, 0)
ring_setting_return.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)

#铃声设置的名称
ring_setting_value = lv.label(ring_screen)
ring_setting_value.set_pos(45, 51)
ring_setting_value.set_size(50, 12)
ring_setting_value.set_text("")
ring_setting_value.set_long_mode(lv.label.LONG.WRAP)
ring_setting_value.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
ring_setting_value.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)

############################音量选择界面
ring_select_screen = lv.obj()
# add style for sound_screen
ring_select_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#铃声选择标题
ring_select_title = lv.label(ring_select_screen)
ring_select_title.set_pos(0, 0)
ring_select_title.set_size(128, 52)
ring_select_title.set_text("铃声选择")
ring_select_title.set_long_mode(lv.label.LONG.WRAP)
ring_select_title.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
ring_select_title.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#铃声列表样式
ring_select_list = lv.list(ring_select_screen)
ring_select_list.set_pos(0, 40)
ring_select_list.set_size(128, 32)
ring_select_list.set_style_pad_left(2, 0)
ring_select_list.set_style_pad_top(4, 0)
ring_select_list.set_style_pad_row(3, 0)
ring_select_list.add_style(style_cont_white, lv.PART.MAIN | lv.STATE.DEFAULT)
ring_select_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
ring_select_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.SCROLLED)
#铃声设置状态栏 选择
ring_select_sure = lv.label(ring_select_screen)
ring_select_sure.set_pos(0, 51)
ring_select_sure.set_size(24, 12)
ring_select_sure.set_text("保存")
ring_select_sure.set_long_mode(lv.label.LONG.WRAP)
ring_select_sure.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
ring_select_sure.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#音量设置状态栏 返回
ring_select_return = lv.label(ring_select_screen)
ring_select_return.set_pos(103, 51)
ring_select_return.set_size(24, 12)
ring_select_return.set_text("返回")
ring_select_return.set_long_mode(lv.label.LONG.WRAP)
ring_select_return.set_style_text_align(lv.TEXT_ALIGN.RIGHT, 0)
ring_select_return.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
############################显示设置界面
display_screen = lv.obj()
display_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#话机设置
display_list_title = lv.label(display_screen)
display_list_title.set_pos(0, 0)
display_list_title.set_size(128, 52)
display_list_title.set_text("显示设置")
display_list_title.set_long_mode(lv.label.LONG.WRAP)
display_list_title.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
display_list_title.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#话机设置列表
display_list = lv.list(display_screen)
display_list.set_pos(0, 16)
display_list.set_size(128, 16)
display_list.set_style_pad_left(0, 0)
display_list.set_style_pad_top(0, 1)
display_list.set_style_pad_row(0, 0)
display_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
display_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
#设置页面确认按钮样式
display_list_value = lv.label(display_screen)
display_list_value.set_pos(52, 51)
display_list_value.set_size(39, 12)
display_list_value.set_text("10秒")
display_list_value.set_long_mode(lv.label.LONG.WRAP)
display_list_value.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
display_list_value.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#设置页面确认按钮样式
display_list_sure = lv.label(display_screen)
display_list_sure.set_pos(0, 51)
display_list_sure.set_size(48, 12)
display_list_sure.set_text("选择")
display_list_sure.set_long_mode(lv.label.LONG.WRAP)
display_list_sure.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
# add style for display_list_sure
display_list_sure.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#设置页面返回按钮样式
display_list_return = lv.label(display_screen)
display_list_return.set_pos(103, 51)
display_list_return.set_size(24, 12)
display_list_return.set_text("返回")
display_list_return.set_long_mode(lv.label.LONG.WRAP)
display_list_return.set_style_text_align(lv.TEXT_ALIGN.RIGHT, 0)
display_list_return.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
############################语言设置
language_screen = lv.obj()
language_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
############################语言设置界面
language_list_title = lv.label(language_screen)
language_list_title.set_pos(0, 0)
language_list_title.set_size(128, 52)
language_list_title.set_text("语言设置")
language_list_title.set_long_mode(lv.label.LONG.WRAP)
language_list_title.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
language_list_title.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
############################语言设置界面
language_list = lv.list(language_screen)
language_list.set_pos(0, 16)
language_list.set_size(128, 16)
language_list.set_style_pad_left(0, 0)
language_list.set_style_pad_top(0, 1)
language_list.set_style_pad_row(0, 0)
language_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
language_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
############################按钮选择界面
language_list_sure = lv.label(language_screen)
language_list_sure.set_pos(0, 51)
language_list_sure.set_size(48, 12)
language_list_sure.set_text("选择")
language_list_sure.set_long_mode(lv.label.LONG.WRAP)
language_list_sure.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
# add style for display_list_sure
language_list_sure.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#设置页面返回按钮样式
language_list_return = lv.label(language_screen)
language_list_return.set_pos(104, 51)
language_list_return.set_size(24, 12)
language_list_return.set_text("返回")
language_list_return.set_long_mode(lv.label.LONG.WRAP)
language_list_return.set_style_text_align(lv.TEXT_ALIGN.RIGHT, 0)
language_list_return.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)

############################背光设置界面
backlight_screen = lv.obj()
backlight_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#话机设置
backlight_list_title = lv.label(backlight_screen)
backlight_list_title.set_pos(0, 0)
backlight_list_title.set_size(128, 52)
backlight_list_title.set_text("背光设置")
backlight_list_title.set_long_mode(lv.label.LONG.WRAP)
backlight_list_title.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
backlight_list_title.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#话机设置列表
backlight_select_list = lv.list(backlight_screen)
backlight_select_list.set_pos(0, 16)
backlight_select_list.set_size(128, 16)
backlight_select_list.set_style_pad_left(0, 0)
backlight_select_list.set_style_pad_top(0, 1)
backlight_select_list.set_style_pad_row(0, 0)
backlight_select_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
backlight_select_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
#设置页面确认按钮样式
backlight_list_sure = lv.label(backlight_screen)
backlight_list_sure.set_pos(0, 51)
backlight_list_sure.set_size(24, 12)
backlight_list_sure.set_text("保存")
backlight_list_sure.set_long_mode(lv.label.LONG.WRAP)
backlight_list_sure.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
# add style for backlight_list_sure
backlight_list_sure.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#设置页面返回按钮样式
backlight_list_return = lv.label(backlight_screen)
backlight_list_return.set_pos(103, 51)
backlight_list_return.set_size(24, 12)
backlight_list_return.set_text("返回")
backlight_list_return.set_long_mode(lv.label.LONG.WRAP)
backlight_list_return.set_style_text_align(lv.TEXT_ALIGN.RIGHT, 0)
backlight_list_return.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)

############################背光开关界面
backlight_switch_screen = lv.obj()
backlight_switch_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#backlight设置
backlight_switch_list_title = lv.label(backlight_switch_screen)
backlight_switch_list_title.set_pos(0, 0)
backlight_switch_list_title.set_size(128, 52)
backlight_switch_list_title.set_text("Backlight Switch")
backlight_switch_list_title.set_long_mode(lv.label.LONG.WRAP)
backlight_switch_list_title.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
backlight_switch_list_title.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#backlight设置列表
backlight_switch_list = lv.list(backlight_switch_screen)
backlight_switch_list.set_pos(0, 16)
backlight_switch_list.set_size(128, 16)
backlight_switch_list.set_style_pad_left(0, 0)
backlight_switch_list.set_style_pad_top(0, 1)
backlight_switch_list.set_style_pad_row(0, 0)
backlight_switch_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
backlight_switch_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
#backlight页面确认按钮样式
backlight_switch_list_sure = lv.label(backlight_switch_screen)
backlight_switch_list_sure.set_pos(0, 51)
backlight_switch_list_sure.set_size(24, 12)
backlight_switch_list_sure.set_text("确定")
backlight_switch_list_sure.set_long_mode(lv.label.LONG.WRAP)
backlight_switch_list_sure.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
backlight_switch_list_sure.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#backlight页面返回按钮样式
backlight_switch_list_return = lv.label(backlight_switch_screen)
backlight_switch_list_return.set_pos(103, 51)
backlight_switch_list_return.set_size(24, 12)
backlight_switch_list_return.set_text("返回")
backlight_switch_list_return.set_long_mode(lv.label.LONG.WRAP)
backlight_switch_list_return.set_style_text_align(lv.TEXT_ALIGN.RIGHT, 0)
backlight_switch_list_return.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)

#显示backlight开关
backlight_switch_screen_value = lv.label(backlight_switch_screen)
backlight_switch_screen_value.set_pos(51, 51)
backlight_switch_screen_value.set_size(48, 12)
backlight_switch_screen_value.set_text("")
backlight_switch_screen_value.set_long_mode(lv.label.LONG.WRAP)
backlight_switch_screen_value.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
backlight_switch_screen_value.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)


############################免打扰开关界面
DND_switch_screen = lv.obj()
DND_switch_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#DND设置
DND_switch_list_title = lv.label(DND_switch_screen)
DND_switch_list_title.set_pos(0, 0)
DND_switch_list_title.set_size(128, 52)
DND_switch_list_title.set_text("DND Switch")
DND_switch_list_title.set_long_mode(lv.label.LONG.WRAP)
DND_switch_list_title.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
DND_switch_list_title.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#DND设置列表
DND_switch_list = lv.list(DND_switch_screen)
DND_switch_list.set_pos(0, 16)
DND_switch_list.set_size(128, 16)
DND_switch_list.set_style_pad_left(0, 0)
DND_switch_list.set_style_pad_top(0, 1)
DND_switch_list.set_style_pad_row(0, 0)
DND_switch_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
DND_switch_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
#DND页面确认按钮样式
DND_switch_list_sure = lv.label(DND_switch_screen)
DND_switch_list_sure.set_pos(0, 51)
DND_switch_list_sure.set_size(24, 12)
DND_switch_list_sure.set_text("确定")
DND_switch_list_sure.set_long_mode(lv.label.LONG.WRAP)
DND_switch_list_sure.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
DND_switch_list_sure.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#DND页面返回按钮样式
DND_switch_list_return = lv.label(DND_switch_screen)
DND_switch_list_return.set_pos(103, 51)
DND_switch_list_return.set_size(24, 12)
DND_switch_list_return.set_text("返回")
DND_switch_list_return.set_long_mode(lv.label.LONG.WRAP)
DND_switch_list_return.set_style_text_align(lv.TEXT_ALIGN.RIGHT, 0)
DND_switch_list_return.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#显示DND开关
DND_switch_screen_value = lv.label(DND_switch_screen)
DND_switch_screen_value.set_pos(51, 51)
DND_switch_screen_value.set_size(48, 12)
DND_switch_screen_value.set_text("")
DND_switch_screen_value.set_long_mode(lv.label.LONG.WRAP)
DND_switch_screen_value.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
DND_switch_screen_value.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)

############################对比度设置界面
contrast_screen = lv.obj()
contrast_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#话机设置
contrast_list_title = lv.label(contrast_screen)
contrast_list_title.set_pos(0, 0)
contrast_list_title.set_size(128, 52)
contrast_list_title.set_text("对比度设置")
contrast_list_title.set_long_mode(lv.label.LONG.WRAP)
contrast_list_title.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
contrast_list_title.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#话机设置列表
contrast_select_list = lv.list(contrast_screen)
contrast_select_list.set_pos(0, 16)
contrast_select_list.set_size(128, 16)
contrast_select_list.set_style_pad_left(0, 0)
contrast_select_list.set_style_pad_top(0, 1)
contrast_select_list.set_style_pad_row(0, 0)
contrast_select_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
contrast_select_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
#设置页面确认按钮样式
contrast_list_sure = lv.label(contrast_screen)
contrast_list_sure.set_pos(0, 51)
contrast_list_sure.set_size(24, 12)
contrast_list_sure.set_text("保存")
contrast_list_sure.set_long_mode(lv.label.LONG.WRAP)
contrast_list_sure.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
# add style for contrast_list_sure
contrast_list_sure.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#设置页面返回按钮样式
contrast_list_return = lv.label(contrast_screen)
contrast_list_return.set_pos(103, 51)
contrast_list_return.set_size(24, 12)
contrast_list_return.set_text("返回")
contrast_list_return.set_long_mode(lv.label.LONG.WRAP)
contrast_list_return.set_style_text_align(lv.TEXT_ALIGN.RIGHT, 0)
contrast_list_return.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
############################安全设置三级界面
security_screen = lv.obj()
security_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#安全设置标题
security_screen_title = lv.label(security_screen)
security_screen_title.set_pos(0, 0)
security_screen_title.set_size(128, 52)
security_screen_title.set_text("安全设置")
security_screen_title.set_long_mode(lv.label.LONG.WRAP)
security_screen_title.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
security_screen_title.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#恢复出厂
security_screen_list = lv.list(security_screen)
security_screen_list.set_pos(0, 16)
security_screen_list.set_size(128, 16)
security_screen_list.set_style_pad_left(0, 0)
security_screen_list.set_style_pad_top(0, 1)
security_screen_list.set_style_pad_row(0, 0)
security_screen_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
security_screen_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
#设置页面确认按钮样式
security_screen_sure = lv.label(security_screen)
security_screen_sure.set_pos(0, 51)
security_screen_sure.set_size(48, 12)
security_screen_sure.set_text("选择")
security_screen_sure.set_long_mode(lv.label.LONG.WRAP)
security_screen_sure.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
# add style for networkservice_list_sure
security_screen_sure.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#设置页面返回按钮样式
security_screen_return = lv.label(security_screen)
security_screen_return.set_pos(103, 51)
security_screen_return.set_size(24, 12)
security_screen_return.set_text("返回")
security_screen_return.set_long_mode(lv.label.LONG.WRAP)
security_screen_return.set_style_text_align(lv.TEXT_ALIGN.RIGHT, 0)
security_screen_return.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
############################恢复出厂设置三级界面
reset_screen = lv.obj()
reset_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
reset_label = lv.label(reset_screen)
reset_label.set_pos(18, 20)
reset_label.set_size(128, 52)
reset_label.set_text("Factory Reset?")
reset_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#恢复出厂页面确认按钮样式
reset_screen_sure = lv.label(reset_screen)
reset_screen_sure.set_pos(0, 51)
reset_screen_sure.set_size(20, 12)
reset_screen_sure.set_text("Yes")
reset_screen_sure.set_long_mode(lv.label.LONG.WRAP)
reset_screen_sure.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
reset_screen_sure.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#恢复出厂页面取消按钮样式
reset_screen_return = lv.label(reset_screen)
reset_screen_return.set_pos(103, 51)
reset_screen_return.set_size(24, 12)
reset_screen_return.set_text("Cancel")
reset_screen_return.set_long_mode(lv.label.LONG.WRAP)
reset_screen_return.set_style_text_align(lv.TEXT_ALIGN.RIGHT, 0)
reset_screen_return.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)

############################开启pin码重启确定界面
pin_restart_screen = lv.obj()
pin_restart_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
pin_restart_label = lv.label(pin_restart_screen)
pin_restart_label.set_pos(5, 20)
pin_restart_label.set_size(128, 52)
pin_restart_label.set_text("需重启生效,立即重启?")
pin_restart_label.set_long_mode(lv.label.LONG.WRAP)
pin_restart_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#恢复出厂页面确认按钮样式
pin_restart_screen_sure = lv.label(pin_restart_screen)
pin_restart_screen_sure.set_pos(0, 51)
pin_restart_screen_sure.set_size(24, 12)
pin_restart_screen_sure.set_text("重启")
pin_restart_screen_sure.set_long_mode(lv.label.LONG.WRAP)
pin_restart_screen_sure.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
pin_restart_screen_sure.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#恢复出厂页面取消按钮样式
repin_restart_screen_return = lv.label(pin_restart_screen)
repin_restart_screen_return.set_pos(103, 51)
repin_restart_screen_return.set_size(24, 12)
repin_restart_screen_return.set_text("取消")
repin_restart_screen_return.set_long_mode(lv.label.LONG.WRAP)
repin_restart_screen_return.set_style_text_align(lv.TEXT_ALIGN.RIGHT, 0)
repin_restart_screen_return.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)


############################PIN码设置三级界面
pinmgr_screen = lv.obj()
pinmgr_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#安全设置标题
pinmgr_screen_title = lv.label(pinmgr_screen)
pinmgr_screen_title.set_pos(0, 0)
pinmgr_screen_title.set_size(128, 16)
pinmgr_screen_title.set_text("PIN码管理")
pinmgr_screen_title.set_long_mode(lv.label.LONG.WRAP)
pinmgr_screen_title.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
pinmgr_screen_title.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#恢复出厂
pinmgr_screen_list = lv.list(pinmgr_screen)
pinmgr_screen_list.set_pos(0, 16)
pinmgr_screen_list.set_size(128, 16)
pinmgr_screen_list.set_style_pad_left(0, 0)
pinmgr_screen_list.set_style_pad_top(0, 1)
pinmgr_screen_list.set_style_pad_row(0, 0)
pinmgr_screen_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
pinmgr_screen_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
#显示pin码设置开关
pinmgr_screen_value = lv.label(pinmgr_screen)
pinmgr_screen_value.set_pos(51, 51)
pinmgr_screen_value.set_size(48, 12)
pinmgr_screen_value.set_text("关闭")
pinmgr_screen_value.set_long_mode(lv.label.LONG.WRAP)
pinmgr_screen_value.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
pinmgr_screen_value.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#设置页面确认按钮样式
pinmgr_screen_sure = lv.label(pinmgr_screen)
pinmgr_screen_sure.set_pos(0, 51)
pinmgr_screen_sure.set_size(48, 12)
pinmgr_screen_sure.set_text("选择")
pinmgr_screen_sure.set_long_mode(lv.label.LONG.WRAP)
pinmgr_screen_sure.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
pinmgr_screen_sure.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#设置页面返回按钮样式
pinmgr_screen_return = lv.label(pinmgr_screen)
pinmgr_screen_return.set_pos(103, 51)
pinmgr_screen_return.set_size(24, 12)
pinmgr_screen_return.set_text("返回")
pinmgr_screen_return.set_long_mode(lv.label.LONG.WRAP)
pinmgr_screen_return.set_style_text_align(lv.TEXT_ALIGN.RIGHT, 0)
pinmgr_screen_return.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
############################网络服务三级界面
networkservice_screen = lv.obj()
networkservice_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#话机设置phone_book_sim_all
networkservice_list_title = lv.label(networkservice_screen)
networkservice_list_title.set_pos(0, 0)
networkservice_list_title.set_size(128, 52)
networkservice_list_title.set_text("网络服务设置")
networkservice_list_title.set_long_mode(lv.label.LONG.SCROLL_CIRCULAR)#字符滚动功能
networkservice_list_title.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
networkservice_list_title.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#话机设置列表
networkservice_list = lv.list(networkservice_screen)
networkservice_list.set_pos(0, 16)
networkservice_list.set_size(128, 16)
networkservice_list.set_style_pad_left(0, 0)
networkservice_list.set_style_pad_top(0, 1)
networkservice_list.set_style_pad_row(0, 0)

networkservice_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
networkservice_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
#设置页面确认按钮样式
networkservice_list_value = lv.label(networkservice_screen)
networkservice_list_value.set_pos(52, 51)
networkservice_list_value.set_size(24, 12)
networkservice_list_value.set_text("关闭")
networkservice_list_value.set_long_mode(lv.label.LONG.WRAP)
networkservice_list_value.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
networkservice_list_value.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#设置页面确认按钮样式
networkservice_list_sure = lv.label(networkservice_screen)
networkservice_list_sure.set_pos(0, 51)
networkservice_list_sure.set_size(48, 12)
networkservice_list_sure.set_text("选择")
networkservice_list_sure.set_long_mode(lv.label.LONG.WRAP)
networkservice_list_sure.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
# add style for networkservice_list_sure
networkservice_list_sure.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#设置页面返回按钮样式
networkservice_list_return = lv.label(networkservice_screen)
networkservice_list_return.set_pos(103, 51)
networkservice_list_return.set_size(24, 12)
networkservice_list_return.set_text("返回")
networkservice_list_return.set_long_mode(lv.label.LONG.WRAP)
networkservice_list_return.set_style_text_align(lv.TEXT_ALIGN.RIGHT, 0)
networkservice_list_return.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)

############################热点开关设置界面
hotspotservice_screen = lv.obj()
hotspotservice_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
hotspotsetting_list_title = lv.label(hotspotservice_screen)
hotspotsetting_list_title.set_pos(0, 0)
hotspotsetting_list_title.set_size(128, 52)
hotspotsetting_list_title.set_text("Hotspot Setting")
hotspotsetting_list_title.set_long_mode(lv.label.LONG.SCROLL_CIRCULAR)#字符滚动功能
hotspotsetting_list_title.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
hotspotsetting_list_title.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#w热点设置列表
hotspotsetting_list = lv.list(hotspotservice_screen)
hotspotsetting_list.set_pos(0, 16)
hotspotsetting_list.set_size(128, 16)
hotspotsetting_list.set_style_pad_left(0, 0)
hotspotsetting_list.set_style_pad_top(0, 1)
hotspotsetting_list.set_style_pad_row(0, 0)
hotspotsetting_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
hotspotsetting_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
#显示热点开关
hotspotsetting_screen_value = lv.label(hotspotservice_screen)
hotspotsetting_screen_value.set_pos(51, 51)
hotspotsetting_screen_value.set_size(48, 12)
hotspotsetting_screen_value.set_text("Close")
hotspotsetting_screen_value.set_long_mode(lv.label.LONG.WRAP)
hotspotsetting_screen_value.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
hotspotsetting_screen_value.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#热点设置页面确认按钮样式
hotspotsetting_list_sure = lv.label(hotspotservice_screen)
hotspotsetting_list_sure.set_pos(0, 51)
hotspotsetting_list_sure.set_size(48, 12)
hotspotsetting_list_sure.set_text("Select")
hotspotsetting_list_sure.set_long_mode(lv.label.LONG.WRAP)
hotspotsetting_list_sure.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
# add style for networkservice_list_sure
hotspotsetting_list_sure.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#热点设置页面返回按钮样式
hotspotsetting_list_return = lv.label(hotspotservice_screen)
hotspotsetting_list_return.set_pos(103, 51)
hotspotsetting_list_return.set_size(24, 12)
hotspotsetting_list_return.set_text("Back")
hotspotsetting_list_return.set_long_mode(lv.label.LONG.WRAP)
hotspotsetting_list_return.set_style_text_align(lv.TEXT_ALIGN.RIGHT, 0)
hotspotsetting_list_return.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)

############################热点编辑界面
hotspot_edit_screen = lv.obj()
hotspot_edit_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
hotspot_edit_screen_title = lv.label(hotspot_edit_screen)
hotspot_edit_screen_title.set_pos(0, 0)
hotspot_edit_screen_title.set_size(128, 52)
hotspot_edit_screen_title.set_text("Hotspot editing")
# hotspot_edit_screen_title.set_long_mode(lv.label.LONG.SCROLL_CIRCULAR)#字符滚动功能
hotspot_edit_screen_title.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
hotspot_edit_screen_title.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#wifi设置列表
hotspot_edit_screen_list = lv.list(hotspot_edit_screen)
hotspot_edit_screen_list.set_pos(0, 16)
hotspot_edit_screen_list.set_size(128, 16)
hotspot_edit_screen_list.set_style_pad_left(0, 0)
hotspot_edit_screen_list.set_style_pad_top(0, 1)
hotspot_edit_screen_list.set_style_pad_row(0, 0)
hotspot_edit_screen_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
hotspot_edit_screen_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
#底部显示SSID
hotspot_edit_screen_value = lv.label(hotspot_edit_screen)
hotspot_edit_screen_value.set_pos(51, 51)
hotspot_edit_screen_value.set_size(48, 12)
hotspot_edit_screen_value.set_text("")
hotspot_edit_screen_value.set_long_mode(lv.label.LONG.WRAP)
hotspot_edit_screen_value.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
hotspot_edit_screen_value.set_long_mode(lv.label.LONG.SCROLL_CIRCULAR)#字符滚动功能
hotspot_edit_screen_value.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#wifi设置页面确认按钮样式
hotspot_edit_screen_sure = lv.label(hotspot_edit_screen)
hotspot_edit_screen_sure.set_pos(0, 51)
hotspot_edit_screen_sure.set_size(48, 12)
hotspot_edit_screen_sure.set_text("Select")
hotspot_edit_screen_sure.set_long_mode(lv.label.LONG.WRAP)
hotspot_edit_screen_sure.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
# add style for networkservice_list_sure
hotspot_edit_screen_sure.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#wifi设置页面返回按钮样式
hotspot_edit_screen_return = lv.label(hotspot_edit_screen)
hotspot_edit_screen_return.set_pos(103, 51)
hotspot_edit_screen_return.set_size(24, 12)
hotspot_edit_screen_return.set_text("Back")
hotspot_edit_screen_return.set_long_mode(lv.label.LONG.WRAP)
hotspot_edit_screen_return.set_style_text_align(lv.TEXT_ALIGN.RIGHT, 0)
hotspot_edit_screen_return.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)


############################呼叫转移三级界面
calltransfer_screen = lv.obj()
calltransfer_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#话机设置
calltransfer_list_title = lv.label(calltransfer_screen)
calltransfer_list_title.set_pos(0, 0)
calltransfer_list_title.set_size(128, 52)
calltransfer_list_title.set_text("呼叫转移")
calltransfer_list_title.set_long_mode(lv.label.LONG.WRAP)
calltransfer_list_title.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
calltransfer_list_title.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#话机设置列表
calltransfer_list = lv.list(calltransfer_screen)
calltransfer_list.set_pos(0, 16)
calltransfer_list.set_size(128, 16)
calltransfer_list.set_style_pad_left(0, 0)
calltransfer_list.set_style_pad_top(0, 1)
calltransfer_list.set_style_pad_row(0, 0)
calltransfer_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
calltransfer_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
#设置页面确认按钮样式
calltransfer_list_value = lv.label(calltransfer_screen)
calltransfer_list_value.set_pos(52, 51)
calltransfer_list_value.set_size(48, 12)
calltransfer_list_value.set_long_mode(lv.label.LONG.WRAP)
calltransfer_list_value.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
calltransfer_list_value.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#设置页面确认按钮样式
calltransfer_list_sure = lv.label(calltransfer_screen)
calltransfer_list_sure.set_pos(0, 51)
calltransfer_list_sure.set_size(48, 12)
calltransfer_list_sure.set_text("选择")
calltransfer_list_sure.set_long_mode(lv.label.LONG.WRAP)
calltransfer_list_sure.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
# add style for calltransfer_list_sure
calltransfer_list_sure.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#设置页面返回按钮样式
calltransfer_list_return = lv.label(calltransfer_screen)
calltransfer_list_return.set_pos(103, 51)
calltransfer_list_return.set_size(24, 12)
calltransfer_list_return.set_text("返回")
calltransfer_list_return.set_long_mode(lv.label.LONG.WRAP)
calltransfer_list_return.set_style_text_align(lv.TEXT_ALIGN.RIGHT, 0)
calltransfer_list_return.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#######volte开关界面
volte_switch_screen = lv.obj()
volte_switch_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#volte设置
volte_switch_list_title = lv.label(volte_switch_screen)
volte_switch_list_title.set_pos(0, 0)
volte_switch_list_title.set_size(128, 52)
volte_switch_list_title.set_text("VOLTE开关")
volte_switch_list_title.set_long_mode(lv.label.LONG.WRAP)
volte_switch_list_title.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
volte_switch_list_title.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#volte设置列表
volte_switch_list = lv.list(volte_switch_screen)
volte_switch_list.set_pos(0, 16)
volte_switch_list.set_size(128, 16)
volte_switch_list.set_style_pad_left(0, 0)
volte_switch_list.set_style_pad_top(0, 1)
volte_switch_list.set_style_pad_row(0, 0)
volte_switch_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
volte_switch_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
#volte页面确认按钮样式
volte_switch_list_sure = lv.label(volte_switch_screen)
volte_switch_list_sure.set_pos(0, 51)
volte_switch_list_sure.set_size(24, 12)
volte_switch_list_sure.set_text("确定")
volte_switch_list_sure.set_long_mode(lv.label.LONG.WRAP)
volte_switch_list_sure.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
volte_switch_list_sure.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#volte页面返回按钮样式
volte_switch_list_return = lv.label(volte_switch_screen)
volte_switch_list_return.set_pos(103, 51)
volte_switch_list_return.set_size(24, 12)
volte_switch_list_return.set_text("返回")
volte_switch_list_return.set_long_mode(lv.label.LONG.WRAP)
volte_switch_list_return.set_style_text_align(lv.TEXT_ALIGN.RIGHT, 0)
volte_switch_list_return.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
############################呼叫转移设置界面
transfer_select_screen = lv.obj()
transfer_select_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#话机设置
transfer_select_list_title = lv.label(transfer_select_screen)
transfer_select_list_title.set_pos(0, 0)
transfer_select_list_title.set_size(128, 52)
transfer_select_list_title.set_text("呼叫转移")
transfer_select_list_title.set_long_mode(lv.label.LONG.SCROLL_CIRCULAR)
transfer_select_list_title.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
transfer_select_list_title.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#话机设置列表
transfer_select_list = lv.list(transfer_select_screen)
transfer_select_list.set_pos(0, 16)
transfer_select_list.set_size(128, 16)
transfer_select_list.set_style_pad_left(0, 0)
transfer_select_list.set_style_pad_top(0, 1)
transfer_select_list.set_style_pad_row(0, 0)
transfer_select_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
transfer_select_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
#设置页面确认按钮样式
transfer_select_list_sure = lv.label(transfer_select_screen)
transfer_select_list_sure.set_pos(0, 51)
transfer_select_list_sure.set_size(24, 12)
transfer_select_list_sure.set_text("确定")
transfer_select_list_sure.set_long_mode(lv.label.LONG.WRAP)
transfer_select_list_sure.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
# add style for transfer_select_list_sure
transfer_select_list_sure.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#设置页面返回按钮样式
transfer_select_list_return = lv.label(transfer_select_screen)
transfer_select_list_return.set_pos(103, 52)
transfer_select_list_return.set_size(24, 12)
transfer_select_list_return.set_text("返回")
transfer_select_list_return.set_long_mode(lv.label.LONG.WRAP)
transfer_select_list_return.set_style_text_align(lv.TEXT_ALIGN.RIGHT, 0)
transfer_select_list_return.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
transfer_select_list_sure.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
############################黑白名单设置界面
black_white_setting_screen = lv.obj()
# add style for sound_screen
black_white_setting_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#黑白名单设置标题
black_white_setting_title = lv.label(black_white_setting_screen)
black_white_setting_title.set_pos(0, 0)
black_white_setting_title.set_size(128, 52)
black_white_setting_title.set_text("黑白名单设置")
black_white_setting_title.set_long_mode(lv.label.LONG.WRAP)
black_white_setting_title.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
black_white_setting_title.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#黑白名单列表样式
black_white_setting_list = lv.list(black_white_setting_screen)
black_white_setting_list.set_pos(0, 16)
black_white_setting_list.set_size(128, 16)
black_white_setting_list.set_style_pad_left(0, 0)
black_white_setting_list.set_style_pad_top(0, 1)
black_white_setting_list.set_style_pad_row(0, 0)
black_white_setting_list.add_style(style_cont_white, lv.PART.MAIN | lv.STATE.DEFAULT)
black_white_setting_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
black_white_setting_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.SCROLLED)
#白名单开关底部显示
black_white_setting_switch = lv.label(black_white_setting_screen)
black_white_setting_switch.set_pos(52, 51)
black_white_setting_switch.set_size(48, 12)
black_white_setting_switch.set_long_mode(lv.label.LONG.WRAP)
black_white_setting_switch.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
black_white_setting_switch.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#黑白名单设置状态栏 选择
black_white_setting_sure = lv.label(black_white_setting_screen)
black_white_setting_sure.set_pos(0, 51)
black_white_setting_sure.set_size(48, 12)
black_white_setting_sure.set_text("选择")
black_white_setting_sure.set_long_mode(lv.label.LONG.WRAP)
black_white_setting_sure.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
black_white_setting_sure.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#黑白名单设置状态栏 返回
black_white_setting_return = lv.label(black_white_setting_screen)
black_white_setting_return.set_pos(103, 51)
black_white_setting_return.set_size(24, 12)
black_white_setting_return.set_text("返回")
black_white_setting_return.set_long_mode(lv.label.LONG.WRAP)
black_white_setting_return.set_style_text_align(lv.TEXT_ALIGN.RIGHT, 0)
black_white_setting_return.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
############################黑/白名单二级界面
black_white_screen = lv.obj()
black_white_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#黑白名单设置标题
black_white_title = lv.label(black_white_screen)
black_white_title.set_pos(0, 0)
black_white_title.set_size(128, 52)
black_white_title.set_text("")
black_white_title.set_long_mode(lv.label.LONG.WRAP)
black_white_title.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
black_white_title.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#黑白名单列表样式
black_white_list = lv.list(black_white_screen)
black_white_list.set_pos(0, 16)
black_white_list.set_size(128, 16)
black_white_list.set_style_pad_left(0, 0)
black_white_list.set_style_pad_top(0, 1)
black_white_list.set_style_pad_row(0, 0)
black_white_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
black_white_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
black_white_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.SCROLLED)
#黑白名单状态栏 选项
black_white_sure = lv.label(black_white_screen)
black_white_sure.set_pos(0, 51)
black_white_sure.set_size(48, 12)
black_white_sure.set_text("选项")
black_white_sure.set_long_mode(lv.label.LONG.WRAP)
black_white_sure.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
black_white_sure.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#黑白名单状态栏 返回
black_white_return = lv.label(black_white_screen)
black_white_return.set_pos(103, 51)
black_white_return.set_size(24, 12)
black_white_return.set_text("返回")
black_white_return.set_long_mode(lv.label.LONG.WRAP)
black_white_return.set_style_text_align(lv.TEXT_ALIGN.RIGHT, 0)
black_white_return.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
############################黑/白名单三级界面
black_white_option_screen = lv.obj()
black_white_option_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#黑白名单列表样式
black_white_option_list = lv.list(black_white_option_screen)
black_white_option_list.set_pos(0, 16)
black_white_option_list.set_size(128, 16)
black_white_option_list.set_style_pad_left(0, 0)
black_white_option_list.set_style_pad_top(0, 1)
black_white_option_list.set_style_pad_row(0, 0)
black_white_option_list.add_style(style_cont_white, lv.PART.MAIN | lv.STATE.DEFAULT)
black_white_option_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
black_white_option_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.SCROLLED)
#黑白名单状态栏 选择
black_white_option_sure = lv.label(black_white_option_screen)
black_white_option_sure.set_pos(0, 51)
black_white_option_sure.set_size(48, 12)
black_white_option_sure.set_text("选择")
black_white_option_sure.set_long_mode(lv.label.LONG.WRAP)
black_white_option_sure.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
black_white_option_sure.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#黑白名单状态栏 返回
black_white_option_return = lv.label(black_white_option_screen)
black_white_option_return.set_pos(103, 51)
black_white_option_return.set_size(24, 12)
black_white_option_return.set_text("返回")
black_white_option_return.set_long_mode(lv.label.LONG.WRAP)
black_white_option_return.set_style_text_align(lv.TEXT_ALIGN.RIGHT, 0)
black_white_option_return.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
############################本机号码显示
local_number_screen = lv.obj()
local_number_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#本机号码标题
local_number_title = lv.label(local_number_screen)
local_number_title.set_pos(0, 0)
local_number_title.set_size(128, 52)
local_number_title.set_text("")
local_number_title.set_long_mode(lv.label.LONG.WRAP)
local_number_title.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
local_number_title.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#第一行
local_number_label = lv.label(local_number_screen)
local_number_label.set_pos(0, 16)
local_number_label.set_size(128, 52)
local_number_label.set_text("本机号码:")
local_number_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
local_number_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#本机号码 返回
local_number_back = lv.label(local_number_screen)
local_number_back.set_pos(103, 51)
local_number_back.set_size(24, 12)
local_number_back.set_text("返回")
local_number_back.set_long_mode(lv.label.LONG.WRAP)
local_number_back.set_style_text_align(lv.TEXT_ALIGN.RIGHT, 0)
local_number_back.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)


phoneinfo_screen = lv.obj()
phoneinfo_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)


phoneinfo_setting_title = lv.label(phoneinfo_screen)
phoneinfo_setting_title.set_pos(0, 0)
phoneinfo_setting_title.set_size(128, 52)
phoneinfo_setting_title.set_text("话机信息")
phoneinfo_setting_title.set_long_mode(lv.label.LONG.WRAP)
phoneinfo_setting_title.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
phoneinfo_setting_title.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#话机信息列表样式
phoneinfo_setting_list = lv.list(phoneinfo_screen)
phoneinfo_setting_list.set_pos(0, 16)
phoneinfo_setting_list.set_size(128, 16)
phoneinfo_setting_list.set_style_pad_left(0, 0)
phoneinfo_setting_list.set_style_pad_top(0, 1)
phoneinfo_setting_list.set_style_pad_row(0, 0)
phoneinfo_setting_list.add_style(style_cont_white, lv.PART.MAIN | lv.STATE.DEFAULT)
phoneinfo_setting_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
phoneinfo_setting_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.SCROLLED)

phoneinfo_setting_sure = lv.label(phoneinfo_screen)
phoneinfo_setting_sure.set_pos(0, 51)
phoneinfo_setting_sure.set_size(48, 12)
phoneinfo_setting_sure.set_text("选择")
phoneinfo_setting_sure.set_long_mode(lv.label.LONG.WRAP)
phoneinfo_setting_sure.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
phoneinfo_setting_sure.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#黑白名单设置状态栏 返回
phoneinfo_setting_return = lv.label(phoneinfo_screen)
phoneinfo_setting_return.set_pos(103, 51)
phoneinfo_setting_return.set_size(24, 12)
phoneinfo_setting_return.set_text("返回")
phoneinfo_setting_return.set_long_mode(lv.label.LONG.WRAP)
phoneinfo_setting_return.set_style_text_align(lv.TEXT_ALIGN.RIGHT, 0)
phoneinfo_setting_return.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)

############################话机版本显示
phone_information_screen = lv.obj()
phone_information_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
phoneinfo_model_label = lv.label(phone_information_screen)
phoneinfo_model_label.set_pos(0, 0)
phoneinfo_model_label.set_size(128, 12)
phoneinfo_model_label.set_text("Model:KT4(1M)")
phoneinfo_model_label.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
phoneinfo_model_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
phoneinfo_os_label = lv.label(phone_information_screen)
phoneinfo_os_label.set_pos(0, 12)
phoneinfo_os_label.set_size(128, 12)
phoneinfo_os_label.set_text("OS:threadx5.0")
phoneinfo_os_label.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
phoneinfo_os_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
phoneinfo_sw_label = lv.label(phone_information_screen)
phoneinfo_sw_label.set_pos(0, 24)
phoneinfo_sw_label.set_size(128, 12)
phoneinfo_sw_label.set_text("SW_VER:KT4(1M)_SIP_1.0.1")
phoneinfo_sw_label.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
phoneinfo_sw_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
phoneinfo_hw_label = lv.label(phone_information_screen)
phoneinfo_hw_label.set_pos(0, 36)
phoneinfo_hw_label.set_size(128, 12)
phoneinfo_hw_label.set_text("HW_VER:03KT4(1B)")
phoneinfo_hw_label.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
phoneinfo_hw_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
line_points = [
    {"x": 0, "y": 0},
    {"x": 128, "y": 0},
]
phone_info_line = lv.line(phone_information_screen)
phone_info_line.set_pos(0, 49)  # 22
phone_info_line.set_size(128, 1)
phone_info_line.set_points(line_points, 2)
phone_info_line.add_style(style_line, lv.PART.MAIN | lv.STATE.DEFAULT)
phoneinfo_netaccess = lv.label(phone_information_screen)
phoneinfo_netaccess.set_pos(0, 51)
phoneinfo_netaccess.set_size(80, 12)
phoneinfo_netaccess.set_text("查看进网许可")
phoneinfo_netaccess.set_long_mode(lv.label.LONG.SCROLL_CIRCULAR)
phoneinfo_netaccess.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
phoneinfo_netaccess.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
phoneinfo_return = lv.label(phone_information_screen)
phoneinfo_return.set_pos(103, 51)
phoneinfo_return.set_size(24, 12)
phoneinfo_return.set_text("返回")
phoneinfo_return.set_long_mode(lv.label.LONG.WRAP)
phoneinfo_return.set_style_text_align(lv.TEXT_ALIGN.RIGHT, 0)
phoneinfo_return.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
############################sip信息显示
sip_information_screen = lv.obj()
sip_information_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
sipinfo_title = lv.label(sip_information_screen)
sipinfo_title.set_pos(0, 0)
sipinfo_title.set_size(128, 10)
sipinfo_title.set_text("IMS信息")
sipinfo_title.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
sipinfo_title.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
sip_information_list = lv.list(sip_information_screen)
sip_information_list.set_pos(0, 16)
sip_information_list.set_size(128, 16)
sip_information_list.set_style_pad_left(0, 0)
sip_information_list.set_style_pad_top(0, 1)
sip_information_list.set_style_pad_row(0, 0)
sip_information_list.add_style(style_cont_white, lv.PART.MAIN | lv.STATE.DEFAULT)
sip_information_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
sip_information_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.SCROLLED)
sip_information_return = lv.label(sip_information_screen)
sip_information_return.set_pos(80, 51)
sip_information_return.set_size(24, 12)
sip_information_return.set_text("返回")
sip_information_return.set_long_mode(lv.label.LONG.WRAP)
sip_information_return.set_style_text_align(lv.TEXT_ALIGN.RIGHT, 0)
sip_information_return.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
############################工厂测试显示
factory_test_screen = lv.obj()
factory_test_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#第一行
factory_test_label1 = lv.label(factory_test_screen)
factory_test_label1.set_pos(0, 2)
factory_test_label1.set_size(128, 12)
factory_test_label1.set_text("")
factory_test_label1.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
factory_test_label1.add_style(style_font_black_montserrat_9h, lv.PART.MAIN | lv.STATE.DEFAULT)
#第二行
factory_test_label2 = lv.label(factory_test_screen)
factory_test_label2.set_pos(0, 14)
factory_test_label2.set_size(128, 12)
factory_test_label2.set_text("")
factory_test_label2.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
# factory_test_label2.set_long_mode(lv.label.LONG.SCROLL_CIRCULAR) #滚动
factory_test_label2.add_style(style_font_black_montserrat_9h, lv.PART.MAIN | lv.STATE.DEFAULT)
#第三行
factory_test_label3 = lv.label(factory_test_screen)
factory_test_label3.set_pos(0, 26)
factory_test_label3.set_size(128, 12)
factory_test_label3.set_text("")
factory_test_label3.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
factory_test_label3.add_style(style_font_black_montserrat_9h, lv.PART.MAIN | lv.STATE.DEFAULT)
#第四行
factory_test_label4 = lv.label(factory_test_screen)
factory_test_label4.set_pos(0, 38)
factory_test_label4.set_size(128, 12)
factory_test_label4.set_text("")
factory_test_label4.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
factory_test_label4.add_style(style_font_black_montserrat_9h, lv.PART.MAIN | lv.STATE.DEFAULT)
#第五行
factory_test_label5 = lv.label(factory_test_screen)
factory_test_label5.set_pos(0, 51)
factory_test_label5.set_size(128, 12)
factory_test_label5.set_text("")
factory_test_label5.set_long_mode(lv.label.LONG.WRAP)
factory_test_label5.set_style_text_align(lv.TEXT_ALIGN.RIGHT, 0)
factory_test_label5.add_style(style_font_black_montserrat_9h, lv.PART.MAIN | lv.STATE.DEFAULT)
############################快捷键设置三级界面
quickkey_screen = lv.obj()
quickkey_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#快捷键设置标题
quickkey_screen_title = lv.label(quickkey_screen)
quickkey_screen_title.set_pos(0, 0)
quickkey_screen_title.set_size(128, 52)
quickkey_screen_title.set_text("快捷设置")
quickkey_screen_title.set_long_mode(lv.label.LONG.WRAP)
quickkey_screen_title.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
quickkey_screen_title.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#快捷键列表
quickkey_list = lv.list(quickkey_screen)
quickkey_list.set_pos(0, 16)
quickkey_list.set_size(128, 16)
quickkey_list.set_style_pad_left(0, 0)
quickkey_list.set_style_pad_top(0, 1)
quickkey_list.set_style_pad_row(0, 0)
quickkey_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
quickkey_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
#设置页面确认按钮样式
quickkey_list_value = lv.label(quickkey_screen)
quickkey_list_value.set_pos(25, 51)
quickkey_list_value.set_size(78, 12)
quickkey_list_value.set_text("13288888888")
quickkey_list_value.set_long_mode(lv.label.LONG.WRAP)
quickkey_list_value.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
quickkey_list_value.set_long_mode(lv.label.LONG.SCROLL_CIRCULAR) #滚动
quickkey_list_value.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#快捷键页面确认按钮样式
quickkey_screen_sure = lv.label(quickkey_screen)
quickkey_screen_sure.set_pos(0, 51)
quickkey_screen_sure.set_size(24, 12)
quickkey_screen_sure.set_text("选项")
quickkey_screen_sure.set_long_mode(lv.label.LONG.WRAP)
quickkey_screen_sure.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
# add style for quickkey_screen_sure
quickkey_screen_sure.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#快捷键页面返回按钮样式
quickkey_screen_return = lv.label(quickkey_screen)
quickkey_screen_return.set_pos(103, 51)
quickkey_screen_return.set_size(24, 12)
quickkey_screen_return.set_text("返回")
quickkey_screen_return.set_long_mode(lv.label.LONG.WRAP)
quickkey_screen_return.set_style_text_align(lv.TEXT_ALIGN.RIGHT, 0)
quickkey_screen_return.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
############################快捷键选项界面
quickkey_option_screen = lv.obj()
quickkey_option_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#快捷键选项列表
quickkey_option_list = lv.list(quickkey_option_screen)
quickkey_option_list.set_pos(0, 16)
quickkey_option_list.set_size(128, 16)
quickkey_option_list.set_style_pad_left(0, 0)
quickkey_option_list.set_style_pad_top(0, 1)
quickkey_option_list.set_style_pad_row(0, 0)
quickkey_option_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
quickkey_option_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
#快捷键选项-选择
quickkey_option_sure = lv.label(quickkey_option_screen)
quickkey_option_sure.set_pos(0, 51)
quickkey_option_sure.set_size(24, 12)
quickkey_option_sure.set_text("选择")
quickkey_option_sure.set_long_mode(lv.label.LONG.WRAP)
quickkey_option_sure.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
quickkey_option_sure.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#快捷键选项-返回
quickkey_option_return = lv.label(quickkey_option_screen)
quickkey_option_return.set_pos(103, 51)
quickkey_option_return.set_size(24, 12)
quickkey_option_return.set_text("返回")
quickkey_option_return.set_long_mode(lv.label.LONG.WRAP)
quickkey_option_return.set_style_text_align(lv.TEXT_ALIGN.RIGHT, 0)
quickkey_option_return.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)

############################更新选择显示界面
update_select_screen = lv.obj()
update_select_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
update_select_screen_label = lv.label(update_select_screen)
update_select_screen_label.set_pos(7, 20)
update_select_screen_label.set_size(128, 52)
update_select_screen_label.set_text("是否重启更新？")
update_select_screen_label.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
update_select_screen_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#
update_select_screen_sure = lv.label(update_select_screen)
update_select_screen_sure.set_pos(0, 51)
update_select_screen_sure.set_size(24, 12)
update_select_screen_sure.set_text("更新")
update_select_screen_sure.set_long_mode(lv.label.LONG.WRAP)
update_select_screen_sure.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
update_select_screen_sure.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#更新页面返回按钮样式
update_select_screen_return = lv.label(update_select_screen)
update_select_screen_return.set_pos(103, 51)
update_select_screen_return.set_size(24, 12)
update_select_screen_return.set_text("取消")
update_select_screen_return.set_long_mode(lv.label.LONG.WRAP)
update_select_screen_return.set_style_text_align(lv.TEXT_ALIGN.RIGHT, 0)
update_select_screen_return.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)

############################更新选择显示界面
update_screen = lv.obj()
update_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
update_screen_label = lv.label(update_screen)
update_screen_label.set_pos(7, 20)
update_screen_label.set_size(128, 52)
update_screen_label.set_text("确认升级更新吗？")
update_screen_label.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
update_screen_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#
update_screen_sure = lv.label(update_screen)
update_screen_sure.set_pos(0, 51)
update_screen_sure.set_size(24, 12)
update_screen_sure.set_text("确认")
update_screen_sure.set_long_mode(lv.label.LONG.WRAP)
update_screen_sure.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
update_screen_sure.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#更新页面返回按钮样式
update_screen_return = lv.label(update_screen)
update_screen_return.set_pos(103, 51)
update_screen_return.set_size(24, 12)  
update_screen_return.set_text("取消")
update_screen_return.set_long_mode(lv.label.LONG.WRAP)
update_screen_return.set_style_text_align(lv.TEXT_ALIGN.RIGHT, 0)
update_screen_return.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)

########################################################################
# description: 开机欢迎页面
# author: pawn
#########################################################################

########################################################################
# description: 开机欢迎页面
# author: pawn
#########################################################################
charging_screen = lv.obj()
charging_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
# 在第一个界面中创建一个label
charging_screen_img = lv.img(charging_screen)
charging_screen_img.set_pos(0, 0)
charging_screen_img.set_size(128,64)
charging_screen_img.set_src("U:/static/Charging5.png")

net_access_code_label = lv.label(charging_screen)
net_access_code_label.set_pos(0, 50)
net_access_code_label.set_size(0, 0)
net_access_code_label.set_text("")
net_access_code_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
net_access_code_label.set_long_mode(lv.label.LONG.WRAP)
net_access_code_label.add_style(style_font_black_montserrat_9h_1, lv.PART.MAIN | lv.STATE.DEFAULT)

net_access_code_label_1 = lv.label(charging_screen)
net_access_code_label_1.set_pos(0, 50)
net_access_code_label_1.set_size(0, 0)
net_access_code_label_1.set_text("")
net_access_code_label_1.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
net_access_code_label_1.set_long_mode(lv.label.LONG.WRAP)
net_access_code_label_1.add_style(style_font_black_montserrat_9h, lv.PART.MAIN | lv.STATE.DEFAULT)

########################################################################
# description: 开机欢迎页面
# author: pawn
#########################################################################
# 在第一个界面中创建一个label
bye_screen = lv.obj()
bye_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
bye_label = lv.label(bye_screen)
# 给这个label设置文本内容
bye_label.set_text("Bye Bye")
# 设置字体样式
bye_label.add_style(style_font_black_montserrat_18, lv.PART.MAIN | lv.STATE.DEFAULT)
# 将文本内容置于文本框的中间
bye_label.center()
########################################################################
# description: 主页面，待机页面
# author: pawn
#########################################################################
main_screen = lv.obj()
main_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#volte图标
#main_volte_img = lv.img(main_screen)
#main_volte_img.set_pos(0, 2)
#main_volte_img.set_size(10, 12)
#main_volte_img.set_src("U:/static/volte.png")
#wifi图标
main_wifi_img = lv.img(main_screen)
main_wifi_img.set_pos(80, 2)
main_wifi_img.set_size(0, 0)
main_wifi_img.set_src("U:/static/wifi.png")
#volte图标
main_data_img = lv.img(main_screen)
main_data_img.set_pos(11, 2)
main_data_img.set_size(7, 12)
main_data_img.set_src("U:/static/data.png")
# 信号格数
main_sign_img = lv.img(main_screen)
main_sign_img.set_pos(18, 2)
main_sign_img.set_size(15, 12)
main_sign_img.set_src("U:/static/signal_5.png")
#来电图标
main_incall_img = lv.img(main_screen)
main_incall_img.set_pos(33, 2)
main_incall_img.set_size(12, 12)
main_incall_img.set_src("U:/static/info_miss.png")
#短信图标
main_sms_img = lv.img(main_screen)
main_sms_img.set_pos(45, 3)
main_sms_img.set_size(12, 12)
main_sms_img.set_src("U:/static/message_n.png")
#sip图标
main_sip_img = lv.img(main_screen)
main_sip_img.set_pos(60, 1)
main_sip_img.set_size(12, 12)
main_sip_img.set_src("U:/static/sip.png")
# 电量图标
main_battery_img = lv.img(main_screen)
main_battery_img.set_pos(105, 2)
main_battery_img.set_size(20, 12)
main_battery_img.set_src("U:/static/charge_battery_4.png")
# 闹铃图标
main_alarm_img = lv.img(main_screen)
main_alarm_img.set_pos(80, 2)
main_alarm_img.set_size(13, 13)
main_alarm_img.set_src("U:/static/alarm.png")

# 网络图标
main_net_connect_img = lv.img(main_screen)
main_net_connect_img.set_pos(80, 2)
main_net_connect_img.set_size(13, 13)
main_net_connect_img.set_src("U:/static/alarm.png")

# 以太网网络连接图标
main_Ethernet_img = lv.img(main_screen)
main_Ethernet_img.set_pos(58, 51)
main_Ethernet_img.set_size(0, 0)
main_Ethernet_img.set_src("U:/static/Wthernet.png")
# 运营商信息
main_operator_label = lv.label(main_screen)
main_operator_label.set_pos(0, 16)
main_operator_label.set_size(128, 14)
main_operator_label.set_text("")
# main_operator_label.set_long_mode(lv.label.LONG.SCROLL_CIRCULAR)
main_operator_label.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
main_operator_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)

# 免打扰
main_disturb_label = lv.label(main_screen)
main_disturb_label.set_pos(30, 51)
main_disturb_label.set_size(64, 14)
main_disturb_label.set_text("")
main_disturb_label.set_long_mode(lv.label.LONG.SCROLL_CIRCULAR)
main_disturb_label.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
main_disturb_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)

# 日期时间信息
main_date_time_label = lv.label(main_screen)
main_date_time_label.set_pos(0, 32)
main_date_time_label.set_size(128, 12)
main_date_time_label.set_text("2023-06-29 15:23")
main_date_time_label.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
main_date_time_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
# Menu菜单
main_menu_label = lv.label(main_screen)
main_menu_label.set_pos(0, 51)
main_menu_label.set_size(48, 12)
main_menu_label.set_text("Menu")

main_menu_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
# 联系人
main_contacts_label = lv.label(main_screen)
main_contacts_label.set_pos(90, 51)
main_contacts_label.set_size(36, 12)
main_contacts_label.set_text("Contacts")
main_contacts_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
########################################################################
# description: 菜单页面
# author: pawn
#########################################################################
# 菜单-第一个显示短信息
menu = lv.obj()
menu.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#
menu_title_label = lv.label(menu)
menu_title_label.set_pos(0, 5)
menu_title_label.set_size(128, 12)
menu_title_label.set_text("Massage")
menu_title_label.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
menu_title_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#
menu_img = lv.img(menu)
menu_img.set_pos(49, 22)
menu_img.set_size(30, 25)
menu_img.set_src("U:/static/menu_message.png")
# 底部功能栏
menu_select_label = lv.label(menu)
menu_select_label.set_pos(0, 51)
menu_select_label.set_size(24, 12)
menu_select_label.set_text("Select")
menu_select_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
menu_back_label = lv.label(menu)
menu_back_label.set_pos(103, 51)
menu_back_label.set_size(24, 12)
menu_back_label.set_text("Back")
menu_back_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
########################################################################
# description: 拨号页面
# author: pawn
#########################################################################
call_screen = lv.obj()
call_screen.set_pos(0, 0)
call_screen.set_size(128, 64)
call_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#创建标题
call_viwe_title_label = lv.label(call_screen)
call_viwe_title_label.set_pos(0, 0)
call_viwe_title_label.set_size(128,12)
call_viwe_title_label.set_text("")
call_viwe_title_label.set_long_mode(lv.label.LONG.SCROLL_CIRCULAR)
call_viwe_title_label.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
call_viwe_title_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
# 创建文本框
call_viwe_screen = lv.obj(call_screen)
call_viwe_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
call_viwe_screen.set_style_pad_all(0, 0)
call_viwe_screen.set_pos(0, 4)
call_viwe_screen.set_size(128, 48)
# 文本标签
call_viwe_info_label_18 = lv.label(call_viwe_screen)
call_viwe_info_label_18.set_pos(0, 0)
call_viwe_info_label_18.set_size(128, 48)
call_viwe_info_label_18.set_width(128)
call_viwe_info_label_18.set_long_mode(lv.label.LONG.WRAP)
call_viwe_info_label_18.set_text("")
call_viwe_info_label_18.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
call_viwe_info_label_18.add_style(style_font_black_montserrat_18, lv.PART.MAIN | lv.STATE.DEFAULT)
call_viwe_info_label_18.add_flag(1)

call_viwe_info_label_20 = lv.label(call_viwe_screen)
call_viwe_info_label_20.set_pos(0, 0)
call_viwe_info_label_20.set_size(128, 48)
call_viwe_info_label_20.set_width(128)
call_viwe_info_label_20.set_long_mode(lv.label.LONG.WRAP)
call_viwe_info_label_20.set_text("")
call_viwe_info_label_20.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
call_viwe_info_label_20.add_style(style_font_black_montserrat_20, lv.PART.MAIN | lv.STATE.DEFAULT)
call_viwe_info_label_18.clear_flag(1)


# 光标
call_viwe_cursor_label = lv.label(call_viwe_screen)
call_viwe_cursor_label.set_pos(0, 22)
call_viwe_cursor_label.set_size(6, 2)
call_viwe_cursor_label.set_text("")
call_viwe_cursor_label.add_style(dark_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
# 底部功能栏
call_viwe_options_label = lv.label(call_screen)
call_viwe_options_label.set_pos(0, 51)
call_viwe_options_label.set_size(32,12)
call_viwe_options_label.set_text("Options")
call_viwe_options_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
call_viwe_back_label = lv.label(call_screen)
call_viwe_back_label.set_pos(103, 51)
call_viwe_back_label.set_size(32, 12)
call_viwe_back_label.set_text("Delete")
call_viwe_back_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
########################################################################
# description: 呼出页面
# author: pawn
#########################################################################
call_out_screen = lv.obj()
call_out_screen.set_pos(0, 0)
call_out_screen.set_size(128, 64)
call_out_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
# 文本标签
call_out_title_label = lv.label(call_out_screen)
call_out_title_label.set_pos(0, 2)
call_out_title_label.set_size(128, 12)
call_out_title_label.set_text("")
call_out_title_label.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
call_out_title_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
# 联系人
call_out_contacts_label = lv.label(call_out_screen)
call_out_contacts_label.set_pos(0, 25)
call_out_contacts_label.set_size(128, 12)
call_out_contacts_label.set_text("")
call_out_contacts_label.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
call_out_contacts_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
# 号码
call_out_phone_number_label = lv.label(call_out_screen)
call_out_phone_number_label.set_pos(0, 45)
call_out_phone_number_label.set_size(128, 12)
call_out_phone_number_label.set_text("")
call_out_phone_number_label.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
call_out_phone_number_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
########################################################################
# description: 通话中页面
# author: pawn
#########################################################################
on_call_screen = lv.obj()
on_call_screen.set_pos(0, 0)
on_call_screen.set_size(128, 64)
on_call_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
# 号码
on_call_phone_name_label = lv.label(on_call_screen)
on_call_phone_name_label.set_pos(0, 0)
on_call_phone_name_label.set_size(128, 16)
on_call_phone_name_label.set_text("")
on_call_phone_name_label.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
on_call_phone_name_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
# 号码
on_call_phone_number_label = lv.label(on_call_screen)
on_call_phone_number_label.set_pos(0, 16)
on_call_phone_number_label.set_size(128, 16)
on_call_phone_number_label.set_text("")
on_call_phone_number_label.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
on_call_phone_number_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
# 文本标签
on_call_title_label = lv.label(on_call_screen)
on_call_title_label.set_pos(4, 32)
on_call_title_label.set_size(64, 12)
on_call_title_label.set_text("时长")
on_call_title_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
on_call_title_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
# 通话时间
on_call_time_label = lv.label(on_call_screen)
on_call_time_label.set_pos(64, 32)
on_call_time_label.set_size(64, 12)
on_call_time_label.set_text("00:00:00")
on_call_time_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
on_call_time_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
# 底部功能栏
on_call_options_label = lv.label(on_call_screen)
on_call_options_label.set_pos(0, 51)
on_call_options_label.set_size(24, 12)
on_call_options_label.set_text("选择")
on_call_options_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
on_call_options_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
on_call_volume_label = lv.label(on_call_screen)
on_call_volume_label.set_pos(52, 51)
on_call_volume_label.set_size(0, 0)
on_call_volume_label.set_text("")
on_call_volume_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
on_call_volume_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
on_call_back_label = lv.label(on_call_screen)
on_call_back_label.set_pos(103, 51)
on_call_back_label.set_size(24, 12)
on_call_back_label.set_text("挂断")
on_call_back_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
on_call_back_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)

############################ussd显示界面
ussd_screen = lv.obj()
ussd_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
ussd_screen_title = lv.label(ussd_screen)
ussd_screen_title.set_pos(0, 0)
ussd_screen_title.set_size(128, 52)
ussd_screen_title.set_text("USSD")
ussd_screen_title.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
ussd_screen_title.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#
ussd_screen_label = lv.label(ussd_screen)
ussd_screen_label.set_pos(7, 20)
ussd_screen_label.set_size(128, 52)
ussd_screen_label.set_text("")
ussd_screen_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
ussd_screen_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)

############################禁拨号码管理主界面
blocked_numbers_main_screen = lv.obj()
blocked_numbers_main_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#禁拨号码管理标题
blocked_numbers_main_title = lv.label(blocked_numbers_main_screen)
blocked_numbers_main_title.set_pos(0, 2)
blocked_numbers_main_title.set_size(128, 48)
blocked_numbers_main_title.set_text("禁拨号码管理")
blocked_numbers_main_title.set_long_mode(lv.label.LONG.SCROLL_CIRCULAR)
blocked_numbers_main_title.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
blocked_numbers_main_title.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#禁拨号码管理列表
blocked_numbers_main_list = lv.list(blocked_numbers_main_screen)
blocked_numbers_main_list.set_pos(0, 16)
blocked_numbers_main_list.set_size(128, 32)
blocked_numbers_main_list.set_style_pad_left(0, 0)
blocked_numbers_main_list.set_style_pad_top(0, 1)
blocked_numbers_main_list.set_style_pad_row(0, 0)
blocked_numbers_main_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
blocked_numbers_main_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
#禁拨号码管理页面确认按钮样式
blocked_numbers_main_sure = lv.label(blocked_numbers_main_screen)
blocked_numbers_main_sure.set_pos(0, 51)
blocked_numbers_main_sure.set_size(48, 12)
blocked_numbers_main_sure.set_text("选择")
blocked_numbers_main_sure.set_long_mode(lv.label.LONG.WRAP)
blocked_numbers_main_sure.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
blocked_numbers_main_sure.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#禁拨号码管理页面返回按钮样式
blocked_numbers_main_return = lv.label(blocked_numbers_main_screen)
blocked_numbers_main_return.set_pos(104, 51)
blocked_numbers_main_return.set_size(24, 12)
blocked_numbers_main_return.set_text("返回")
blocked_numbers_main_return.set_long_mode(lv.label.LONG.WRAP)
blocked_numbers_main_return.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
blocked_numbers_main_return.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)

# 禁拨号码列表界面已移除以节省内存

#
#更新页面返回按钮样式
ussd_screen_return = lv.label(ussd_screen)
ussd_screen_return.set_pos(103, 51)
ussd_screen_return.set_size(24, 12)  
ussd_screen_return.set_text("Back")
ussd_screen_return.set_long_mode(lv.label.LONG.WRAP)
ussd_screen_return.set_style_text_align(lv.TEXT_ALIGN.RIGHT, 0)
ussd_screen_return.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)

########################################################################
# description: 新加入的第三方通话中页面
# author: pawn
#########################################################################
on_call_new_screen = lv.obj()
on_call_new_screen.set_pos(0, 0)
on_call_new_screen.set_size(128, 64)
on_call_new_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
# 号码
on_call_new_phone_name_label = lv.label(on_call_new_screen)
on_call_new_phone_name_label.set_pos(0, 0)
on_call_new_phone_name_label.set_size(128, 16)
on_call_new_phone_name_label.set_text("")
on_call_new_phone_name_label.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
on_call_new_phone_name_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
# 号码
on_call_new_phone_number_label = lv.label(on_call_new_screen)
on_call_new_phone_number_label.set_pos(0, 16)
on_call_new_phone_number_label.set_size(128, 16)
on_call_new_phone_number_label.set_text("")
on_call_new_phone_number_label.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
on_call_new_phone_number_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
# 文本标签
on_call_new_title_label = lv.label(on_call_new_screen)
on_call_new_title_label.set_pos(4, 32)
on_call_new_title_label.set_size(64, 12)
on_call_new_title_label.set_text("时长")
on_call_new_title_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
on_call_new_title_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
# 通话时间
on_call_new_time_label = lv.label(on_call_new_screen)
on_call_new_time_label.set_pos(64, 32)
on_call_new_time_label.set_size(64, 12)
on_call_new_time_label.set_text("00:00:00")
on_call_new_time_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
on_call_new_time_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
# 底部功能栏
on_call_new_options_label = lv.label(on_call_new_screen)
on_call_new_options_label.set_pos(0, 51)
on_call_new_options_label.set_size(24, 12)
on_call_new_options_label.set_text("选择")
on_call_new_options_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
on_call_new_options_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
on_call_new_volume_label = lv.label(on_call_screen)
on_call_new_volume_label.set_pos(52, 51)
on_call_new_volume_label.set_size(0, 0)
on_call_new_volume_label.set_text("")
on_call_new_volume_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
on_call_new_volume_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
on_call_new_back_label = lv.label(on_call_new_screen)
on_call_new_back_label.set_pos(103, 51)
on_call_new_back_label.set_size(24, 12)
on_call_new_back_label.set_text("挂断")
on_call_new_back_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
on_call_new_back_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
########################################################################
# description: 通话中菜单界面
# author: pawn
#########################################################################
on_call_select_screen = lv.obj()
on_call_select_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#通话菜单标题
on_call_select_screen_title = lv.label(on_call_select_screen)
on_call_select_screen_title.set_pos(0, 2)
on_call_select_screen_title.set_size(128, 48)
on_call_select_screen_title.set_text("通话菜单")
on_call_select_screen_title.set_long_mode(lv.label.LONG.WRAP)
on_call_select_screen_title.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
on_call_select_screen_title.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#通话菜单列表
on_call_select_screen_list = lv.list(on_call_select_screen)
on_call_select_screen_list.set_pos(0, 16)
on_call_select_screen_list.set_size(128, 16)
on_call_select_screen_list.set_style_pad_left(0, 0)
on_call_select_screen_list.set_style_pad_top(0, 1)
on_call_select_screen_list.set_style_pad_row(0, 0)
on_call_select_screen_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
on_call_select_screen_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
#通话菜单确认按钮样式
on_call_select_screen_sure = lv.label(on_call_select_screen)
on_call_select_screen_sure.set_pos(0, 51)
on_call_select_screen_sure.set_size(24, 12)
on_call_select_screen_sure.set_text("选择")
on_call_select_screen_sure.set_long_mode(lv.label.LONG.WRAP)
on_call_select_screen_sure.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
# add style for setting_list_sure
on_call_select_screen_sure.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#通话菜单返回按钮样式
on_call_select_screen_return = lv.label(on_call_select_screen)
on_call_select_screen_return.set_pos(103, 51)
on_call_select_screen_return.set_size(24, 12)
on_call_select_screen_return.set_text("返回")
on_call_select_screen_return.set_long_mode(lv.label.LONG.WRAP)
on_call_select_screen_return.set_style_text_align(lv.TEXT_ALIGN.RIGHT, 0)
on_call_select_screen_return.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
############################快捷拨号界面
speed_dial_screen = lv.obj()
speed_dial_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#快捷拨号列表
speed_dial_screen_list = lv.list(speed_dial_screen)
speed_dial_screen_list.set_pos(0, 16)
speed_dial_screen_list.set_size(128, 16)
speed_dial_screen_list.set_style_pad_left(0, 0)
speed_dial_screen_list.set_style_pad_top(0, 1)
speed_dial_screen_list.set_style_pad_row(0, 0)
speed_dial_screen_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
speed_dial_screen_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
#快捷拨号选项按钮样式
speed_dial_screen_select = lv.label(speed_dial_screen)
speed_dial_screen_select.set_pos(0, 51)
speed_dial_screen_select.set_size(24, 12)
speed_dial_screen_select.set_text("选项")
speed_dial_screen_select.set_long_mode(lv.label.LONG.WRAP)
speed_dial_screen_select.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
speed_dial_screen_select.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#通话菜单返回按钮样式
speed_dial_screen_return = lv.label(speed_dial_screen)
speed_dial_screen_return.set_pos(103, 51)
speed_dial_screen_return.set_size(24, 12)
speed_dial_screen_return.set_text("返回")
speed_dial_screen_return.set_long_mode(lv.label.LONG.WRAP)
speed_dial_screen_return.set_style_text_align(lv.TEXT_ALIGN.RIGHT, 0)
speed_dial_screen_return.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
############################快捷拨号选项界面
speed_dial_option_screen = lv.obj()
speed_dial_option_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#
speed_dial_option_list = lv.list(speed_dial_option_screen)
speed_dial_option_list.set_pos(0, 0)
speed_dial_option_list.set_size(128, 36)
speed_dial_option_list.set_style_pad_left(0, 0)
speed_dial_option_list.set_style_pad_top(0, 0)
speed_dial_option_list.set_style_pad_row(0, 0)
speed_dial_option_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
speed_dial_option_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
speed_dial_option_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.SCROLLED)
# 底部功能栏
speed_dial_options_select_label = lv.label(speed_dial_option_screen)
speed_dial_options_select_label.set_pos(0, 51)
speed_dial_options_select_label.set_size(48, 12)
speed_dial_options_select_label.set_text("确认")
speed_dial_options_select_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
speed_dial_options_select_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
speed_dial_options_back_label = lv.label(speed_dial_option_screen)
speed_dial_options_back_label.set_pos(103, 51)
speed_dial_options_back_label.set_size(24, 12)
speed_dial_options_back_label.set_text("返回")
speed_dial_options_back_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
speed_dial_options_back_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)

# ############################通话中呼叫转移界面
# call_request_screen = lv.obj()
# call_request_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
# call_request_label = lv.label(call_request_screen)
# call_request_label.set_pos(18, 20)
# call_request_label.set_size(128, 52)
# call_request_label.set_text("新通话申请")
# call_request_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
# #通话中呼叫页面确认按钮样式
# call_request_screen_sure = lv.label(call_request_screen)
# call_request_screen_sure.set_pos(0, 51)
# call_request_screen_sure.set_size(12, 12)
# call_request_screen_sure.set_text("接听")
# call_request_screen_sure.set_long_mode(lv.label.LONG.WRAP)
# call_request_screen_sure.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
# call_request_screen_sure.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
# #通话中呼叫页面取消按钮样式
# call_request_screen_return = lv.label(call_request_screen)
# call_request_screen_return.set_pos(103, 51)
# call_request_screen_return.set_size(24, 12)
# call_request_screen_return.set_text("拒绝")
# call_request_screen_return.set_long_mode(lv.label.LONG.WRAP)
# call_request_screen_return.set_style_text_align(lv.TEXT_ALIGN.RIGHT, 0)
# call_request_screen_return.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)

########################################################################
# description: 通话结束
# author: pawn
#########################################################################
end_call_screen = lv.obj()
end_call_screen.set_pos(0, 0)
end_call_screen.set_size(128, 64)
end_call_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
# 文本标签
end_call_title_label = lv.label(end_call_screen)
end_call_title_label.set_pos(0, 5)
end_call_title_label.set_size(128, 12)
end_call_title_label.set_text("结束通话")
end_call_title_label.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
end_call_title_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
# 号码
end_call_phone_number_label = lv.label(end_call_screen)
end_call_phone_number_label.set_pos(0, 20)
end_call_phone_number_label.set_size(128, 12)
end_call_phone_number_label.set_text("13866668888")
end_call_phone_number_label.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
end_call_phone_number_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
# 通话时间
end_call_time_label = lv.label(end_call_screen)
end_call_time_label.set_pos(0, 45)
end_call_time_label.set_size(128, 12)
end_call_time_label.set_text("00:00:10")
end_call_time_label.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
end_call_time_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
########################################################################
# description: 来电页面
# author: pawn
#########################################################################
call_up_screen = lv.obj()
call_up_screen.set_pos(0, 0)
call_up_screen.set_size(128, 64)
call_up_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
# 文本标签
call_up_title_label = lv.label(call_up_screen)
call_up_title_label.set_pos(0, 0)
call_up_title_label.set_size(128, 16)
call_up_title_label.set_text("来电")
call_up_title_label.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
call_up_title_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
# 联系人
call_up_contacts_label = lv.label(call_up_screen)
call_up_contacts_label.set_pos(0, 16)
call_up_contacts_label.set_size(128, 12)
call_up_contacts_label.set_text("")
call_up_contacts_label.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
call_up_contacts_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
# 号码
call_up_phone_number_label = lv.label(call_up_screen)
call_up_phone_number_label.set_pos(0, 32)
call_up_phone_number_label.set_size(128, 12)
call_up_phone_number_label.set_text("")
call_up_phone_number_label.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
call_up_phone_number_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#已静音
call_up_phone_mute_label = lv.label(call_up_screen)
call_up_phone_mute_label.set_pos(46, 51)
call_up_phone_mute_label.set_size(36, 12)
call_up_phone_mute_label.set_text("")
call_up_phone_mute_label.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
call_up_phone_mute_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
# 底部功能栏
call_up_ignore_label = lv.label(call_up_screen)
call_up_ignore_label.set_pos(0, 51)
call_up_ignore_label.set_size(24, 14)
call_up_ignore_label.set_text("忽略")
call_up_ignore_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
call_up_ignore_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
call_up_back_label = lv.label(call_up_screen)
call_up_back_label.set_pos(103, 51)
call_up_back_label.set_size(24, 14)
call_up_back_label.set_text("挂断")
call_up_back_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
call_up_back_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
########################################################################
# description: 来电页面
# author: pawn
#########################################################################
call_up_new_screen = lv.obj()
call_up_new_screen.set_pos(0, 0)
call_up_new_screen.set_size(128, 64)
call_up_new_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
# 文本标签
call_up_new_title_label = lv.label(call_up_new_screen)
call_up_new_title_label.set_pos(0, 0)
call_up_new_title_label.set_size(128, 16)
call_up_new_title_label.set_text("来电")
call_up_new_title_label.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
call_up_new_title_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
# 联系人
call_up_new_contacts_label = lv.label(call_up_new_screen)
call_up_new_contacts_label.set_pos(0, 16)
call_up_new_contacts_label.set_size(128, 12)
call_up_new_contacts_label.set_text("")
call_up_new_contacts_label.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
call_up_new_contacts_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
# 号码
call_up_new_phone_number_label = lv.label(call_up_new_screen)
call_up_new_phone_number_label.set_pos(0, 32)
call_up_new_phone_number_label.set_size(128, 12)
call_up_new_phone_number_label.set_text("")
call_up_new_phone_number_label.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
call_up_new_phone_number_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
# 底部功能栏
call_up_new_ignore_label = lv.label(call_up_new_screen)
call_up_new_ignore_label.set_pos(0, 51)
call_up_new_ignore_label.set_size(54, 14)
call_up_new_ignore_label.set_text("忽略")
call_up_new_ignore_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
call_up_new_ignore_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
call_up_new_back_label = lv.label(call_up_new_screen)
call_up_new_back_label.set_pos(103, 51)
call_up_new_back_label.set_size(24, 14)
call_up_new_back_label.set_text("挂断")
call_up_new_back_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
call_up_new_back_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
########################################################################
# description: 电话本
# author: pawn
#########################################################################
phone_book = lv.obj()
phone_book.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#
phone_book_title = lv.label(phone_book)
phone_book_title.set_pos(0, 2)
phone_book_title.set_size(128, 14)
phone_book_title.set_text("电话本")
phone_book_title.set_long_mode(lv.label.LONG.WRAP)
phone_book_title.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
phone_book_title.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#
phone_book_list = lv.list(phone_book)
phone_book_list.set_pos(0, 14)
phone_book_list.set_size(128, 36)
phone_book_list.set_style_pad_left(0, 0)
phone_book_list.set_style_pad_top(0, 1)
phone_book_list.set_style_pad_row(0, 0)
phone_book_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
phone_book_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
phone_book_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.SCROLLED)
# 底部功能栏
phone_book_select_label = lv.label(phone_book)
phone_book_select_label.set_pos(0, 51)
phone_book_select_label.set_size(24, 24)
phone_book_select_label.set_text("选择")
phone_book_select_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
phone_book_select_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
phone_book_back_label = lv.label(phone_book)
phone_book_back_label.set_pos(103, 51)
phone_book_back_label.set_size(24, 12)
phone_book_back_label.set_text("返回")
phone_book_back_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
phone_book_back_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
########################################################################
# description: 通讯录电话本选择
# author: pawn
#########################################################################
phone_book_com_select = lv.obj()
phone_book_com_select.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#电话本存储选择标题
phone_book_com_select_title = lv.label(phone_book_com_select)
phone_book_com_select_title.set_pos(0, 0)
phone_book_com_select_title.set_size(128, 52)
phone_book_com_select_title.set_text("电话本")
phone_book_com_select_title.set_long_mode(lv.label.LONG.WRAP)
phone_book_com_select_title.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
phone_book_com_select_title.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#电话本存储选择确定
phone_book_com_select_sure_label = lv.label(phone_book_com_select)
phone_book_com_select_sure_label.set_pos(0, 51)
phone_book_com_select_sure_label.set_size(24, 24)
phone_book_com_select_sure_label.set_text("选择")
phone_book_com_select_sure_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
phone_book_com_select_sure_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#电话本存储选择列表样式
phone_book_com_select_list = lv.list(phone_book_com_select)
phone_book_com_select_list.set_pos(0, 40)
phone_book_com_select_list.set_size(128, 32)
phone_book_com_select_list.set_style_pad_left(2, 0)
phone_book_com_select_list.set_style_pad_top(4, 0)
phone_book_com_select_list.set_style_pad_row(3, 0)
phone_book_com_select_list.add_style(style_cont_white, lv.PART.MAIN | lv.STATE.DEFAULT)
phone_book_com_select_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
phone_book_com_select_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.SCROLLED)
#电话本存储选择返回
phone_book_com_select_back_label = lv.label(phone_book_com_select)
phone_book_com_select_back_label.set_pos(103, 51)
phone_book_com_select_back_label.set_size(24, 12)
phone_book_com_select_back_label.set_text("返回")
phone_book_com_select_back_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
phone_book_com_select_back_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)

########################################################################
# description: 电话本查看选择
# author: pawn
#########################################################################
phone_book_select = lv.obj()
phone_book_select.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#电话本存储选择标题
phone_book_select_title = lv.label(phone_book_select)
phone_book_select_title.set_pos(0, 0)
phone_book_select_title.set_size(128, 52)
phone_book_select_title.set_text("电话本")
phone_book_select_title.set_long_mode(lv.label.LONG.WRAP)
phone_book_select_title.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
phone_book_select_title.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#电话本存储选择确定
phone_book_select_sure_label = lv.label(phone_book_select)
phone_book_select_sure_label.set_pos(0, 51)
phone_book_select_sure_label.set_size(24, 24)
phone_book_select_sure_label.set_text("选择")
phone_book_select_sure_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
phone_book_select_sure_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#电话本存储选择列表样式
phone_book_select_list = lv.list(phone_book_select)
phone_book_select_list.set_pos(0, 40)
phone_book_select_list.set_size(128, 32)
phone_book_select_list.set_style_pad_left(2, 0)
phone_book_select_list.set_style_pad_top(4, 0)
phone_book_select_list.set_style_pad_row(3, 0)
phone_book_select_list.add_style(style_cont_white, lv.PART.MAIN | lv.STATE.DEFAULT)
phone_book_select_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
phone_book_select_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.SCROLLED)
#电话本存储选择返回
phone_book_select_back_label = lv.label(phone_book_select)
phone_book_select_back_label.set_pos(103, 51)
phone_book_select_back_label.set_size(24, 12)
phone_book_select_back_label.set_text("返回")
phone_book_select_back_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
phone_book_select_back_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
########################################################################
# description: 电话本存储选择
# author: pawn
#########################################################################
phone_book_store_select = lv.obj()
phone_book_store_select.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#电话本存储选择标题
phone_book_store_select_title = lv.label(phone_book_store_select)
phone_book_store_select_title.set_pos(0, 0)
phone_book_store_select_title.set_size(128, 52)
phone_book_store_select_title.set_text("存储选择")
phone_book_store_select_title.set_long_mode(lv.label.LONG.WRAP)
phone_book_store_select_title.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
phone_book_store_select_title.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#电话本存储选择确定
phone_book_store_select_sure_label = lv.label(phone_book_store_select)
phone_book_store_select_sure_label.set_pos(0, 51)
phone_book_store_select_sure_label.set_size(24, 24)
phone_book_store_select_sure_label.set_text("选择")
phone_book_store_select_sure_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
phone_book_store_select_sure_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#电话本存储选择列表样式
phone_book_store_select_list = lv.list(phone_book_store_select)
phone_book_store_select_list.set_pos(0, 40)
phone_book_store_select_list.set_size(128, 32)
phone_book_store_select_list.set_style_pad_left(2, 0)
phone_book_store_select_list.set_style_pad_top(4, 0)
phone_book_store_select_list.set_style_pad_row(3, 0)
phone_book_store_select_list.add_style(style_cont_white, lv.PART.MAIN | lv.STATE.DEFAULT)
phone_book_store_select_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
phone_book_store_select_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.SCROLLED)
#电话本存储选择返回
phone_book_store_select_back_label = lv.label(phone_book_store_select)
phone_book_store_select_back_label.set_pos(103, 51)
phone_book_store_select_back_label.set_size(24, 12)
phone_book_store_select_back_label.set_text("返回")
phone_book_store_select_back_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
phone_book_store_select_back_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
########################################################################
# description: 电话本查看全部
# author: pawn
#########################################################################
phone_book_all_screen = lv.obj()
phone_book_all_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#
phone_book_all_list = lv.list(phone_book_all_screen)
phone_book_all_list.set_pos(0, 0)
phone_book_all_list.set_size(128, 50)
phone_book_all_list.set_style_pad_left(0, 0)
phone_book_all_list.set_style_pad_top(0, 0)
phone_book_all_list.set_style_pad_row(1, 0)
phone_book_all_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
phone_book_all_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
phone_book_all_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.SCROLLED)
#
phone_book_all_list_btn = lv.btn(phone_book_all_list)
phone_book_all_list_btn.set_pos(0, 0)
phone_book_all_list_btn.set_size(128, 16)
phone_book_all_list_btn.add_style(style_list_black, lv.PART.MAIN | lv.STATE.DEFAULT)
#
phone_book_all_list_label = lv.label(phone_book_all_list_btn)
phone_book_all_list_label.set_pos(2, 2)
phone_book_all_list_label.set_size(126, 14)
phone_book_all_list_label.set_text("大哥")
# 底部功能栏
phone_book_all_options_label = lv.label(phone_book_all_screen)
phone_book_all_options_label.set_pos(0, 51)
phone_book_all_options_label.set_size(24, 12)
phone_book_all_options_label.set_text("发送")
phone_book_all_options_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
phone_book_all_options_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#显示内存数
phone_book_all_memory_value = lv.label(phone_book_all_screen)
phone_book_all_memory_value.set_pos(48, 51)
phone_book_all_memory_value.set_size(48, 12)
phone_book_all_memory_value.set_text("")
phone_book_all_memory_value.set_long_mode(lv.label.LONG.WRAP)
phone_book_all_memory_value.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
phone_book_all_memory_value.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)

phone_book_all_back_label = lv.label(phone_book_all_screen)
phone_book_all_back_label.set_pos(103, 51)
phone_book_all_back_label.set_size(24, 12)
phone_book_all_back_label.set_text("返回")
phone_book_all_back_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
phone_book_all_back_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
########################################################################
# description: 电话本sim卡查看全部
# author: pawn
#########################################################################
phone_book_sim_all_screen = lv.obj()
phone_book_sim_all_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#
phone_book_sim_all_list = lv.list(phone_book_sim_all_screen)
phone_book_sim_all_list.set_pos(0, 0)
phone_book_sim_all_list.set_size(128, 50)
phone_book_sim_all_list.set_style_pad_left(0, 0)
phone_book_sim_all_list.set_style_pad_top(0, 0)
phone_book_sim_all_list.set_style_pad_row(1, 0)
phone_book_sim_all_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
phone_book_sim_all_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
phone_book_sim_all_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.SCROLLED)
#
phone_book_all_sim_list_btn = lv.btn(phone_book_sim_all_list)
phone_book_all_sim_list_btn.set_pos(0, 0)
phone_book_all_sim_list_btn.set_size(128, 16)
phone_book_all_sim_list_btn.add_style(style_list_black, lv.PART.MAIN | lv.STATE.DEFAULT)
#
phone_book_all_sim_list_label = lv.label(phone_book_all_sim_list_btn)
phone_book_all_sim_list_label.set_pos(2, 2)
phone_book_all_sim_list_label.set_size(126, 14)
phone_book_all_sim_list_label.set_text("大哥")
# 底部功能栏
phone_book_all_sim_options_label = lv.label(phone_book_sim_all_screen)
phone_book_all_sim_options_label.set_pos(0, 51)
phone_book_all_sim_options_label.set_size(24, 12)
phone_book_all_sim_options_label.set_text("选项")
phone_book_all_sim_options_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
phone_book_all_sim_options_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#显示内存数
phone_book_all_sim_memory_value = lv.label(phone_book_sim_all_screen)
phone_book_all_sim_memory_value.set_pos(48, 51)
phone_book_all_sim_memory_value.set_size(48, 12)
phone_book_all_sim_memory_value.set_text("")
phone_book_all_sim_memory_value.set_long_mode(lv.label.LONG.WRAP)
phone_book_all_sim_memory_value.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
phone_book_all_sim_memory_value.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)

phone_book_all_sim_back_label = lv.label(phone_book_sim_all_screen)
phone_book_all_sim_back_label.set_pos(103, 51)
phone_book_all_sim_back_label.set_size(24, 12)
phone_book_all_sim_back_label.set_text("返回")
phone_book_all_sim_back_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
phone_book_all_sim_back_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
########################################################################
# description: 电话本拷贝界面
# author: pawn
#########################################################################
phone_book_copy_screen = lv.obj()
phone_book_copy_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#电话本拷贝标题
phone_book_copy_title = lv.label(phone_book_copy_screen)
phone_book_copy_title.set_pos(0, 0)
phone_book_copy_title.set_size(128, 52)
phone_book_copy_title.set_text("电话拷贝")
phone_book_copy_title.set_long_mode(lv.label.LONG.WRAP)
phone_book_copy_title.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
phone_book_copy_title.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#电话本拷贝列表样式
phone_book_copy_list = lv.list(phone_book_copy_screen)
phone_book_copy_list.set_pos(0, 40)
phone_book_copy_list.set_size(128, 32)
phone_book_copy_list.set_style_pad_left(2, 0)
phone_book_copy_list.set_style_pad_top(4, 0)
phone_book_copy_list.set_style_pad_row(3, 0)
phone_book_copy_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
phone_book_copy_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
phone_book_copy_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.SCROLLED)
# 底部功能栏
phone_book_copy_select_label = lv.label(phone_book_copy_screen)
phone_book_copy_select_label.set_pos(0, 51)
phone_book_copy_select_label.set_size(48, 12)
phone_book_copy_select_label.set_text("选择")
phone_book_copy_select_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
phone_book_copy_select_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
phone_book_copy_back_label = lv.label(phone_book_copy_screen)
phone_book_copy_back_label.set_pos(103, 51)
phone_book_copy_back_label.set_size(24, 12)
phone_book_copy_back_label.set_text("返回")
phone_book_copy_back_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
phone_book_copy_back_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
########################################################################
# description: 电话本选项
# author: pawn
#########################################################################
phone_book_options_screen = lv.obj()
phone_book_options_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#
phone_book_options_list = lv.list(phone_book_options_screen)
phone_book_options_list.set_pos(0, 0)
phone_book_options_list.set_size(128, 36)
phone_book_options_list.set_style_pad_left(0, 0)
phone_book_options_list.set_style_pad_top(0, 0)
phone_book_options_list.set_style_pad_row(0, 0)
phone_book_options_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
phone_book_options_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
phone_book_options_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.SCROLLED)
# 底部功能栏
phone_book_options_select_label = lv.label(phone_book_options_screen)
phone_book_options_select_label.set_pos(0, 51)
phone_book_options_select_label.set_size(48, 12)
phone_book_options_select_label.set_text("选择")
phone_book_options_select_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
phone_book_options_select_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
phone_book_options_back_label = lv.label(phone_book_options_screen)
phone_book_options_back_label.set_pos(103, 51)
phone_book_options_back_label.set_size(24, 12)
phone_book_options_back_label.set_text("返回")
phone_book_options_back_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
phone_book_options_back_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
########################################################################
# description: 搜索电话本选项
# author: pawn
#########################################################################
phone_book_search_options_screen = lv.obj()
phone_book_search_options_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#
phone_book_search_options_list = lv.list(phone_book_search_options_screen)
phone_book_search_options_list.set_pos(0, 0)
phone_book_search_options_list.set_size(128, 36)
phone_book_search_options_list.set_style_pad_left(0, 0)
phone_book_search_options_list.set_style_pad_top(0, 0)
phone_book_search_options_list.set_style_pad_row(0, 0)
phone_book_search_options_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
phone_book_search_options_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
phone_book_search_options_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.SCROLLED)
# 底部功能栏
phone_book_search_options_select_label = lv.label(phone_book_search_options_screen)
phone_book_search_options_select_label.set_pos(0, 51)
phone_book_search_options_select_label.set_size(48, 12)
phone_book_search_options_select_label.set_text("选择")
phone_book_search_options_select_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
phone_book_search_options_select_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
phone_book_search_options_back_label = lv.label(phone_book_search_options_screen)
phone_book_search_options_back_label.set_pos(103, 51)
phone_book_search_options_back_label.set_size(24, 12)
phone_book_search_options_back_label.set_text("返回")
phone_book_search_options_back_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
phone_book_search_options_back_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
########################################################################
########################################################################
# description: 电话本选项详情页
# author: pawn
#########################################################################
phonge_book_options_details_screen = lv.obj()
phonge_book_options_details_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
# 文本标签
phonge_book_options_details_label = lv.label(phonge_book_options_details_screen)
phonge_book_options_details_label.set_pos(0, 2)
phonge_book_options_details_label.set_size(128, 48)
phonge_book_options_details_label.set_long_mode(lv.label.LONG.WRAP)
phonge_book_options_details_label.set_text("")
phonge_book_options_details_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
phonge_book_options_details_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
# 底部功能栏
phonge_book_options_details_back_label = lv.label(phonge_book_options_details_screen)
phonge_book_options_details_back_label.set_pos(103, 51)
phonge_book_options_details_back_label.set_size(24, 12)
phonge_book_options_details_back_label.set_text("返回")
phonge_book_options_details_back_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
phonge_book_options_details_back_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
########################################################################
# description: 通话记录
# author: pawn
#########################################################################
call_log_screen = lv.obj()
call_log_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#
call_log_title = lv.label(call_log_screen)
call_log_title.set_pos(0, 2)
call_log_title.set_size(128, 14)
call_log_title.set_text("通话记录")
call_log_title.set_long_mode(lv.label.LONG.WRAP)
call_log_title.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
call_log_title.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#
call_log_list = lv.list(call_log_screen)
call_log_list.set_pos(0, 14)
call_log_list.set_size(128, 36)
call_log_list.set_style_pad_left(0, 0)
call_log_list.set_style_pad_top(0, 1)
call_log_list.set_style_pad_row(0, 0)
call_log_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
call_log_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
call_log_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.SCROLLED)
# 底部功能栏
call_log_select_label = lv.label(call_log_screen)
call_log_select_label.set_pos(0, 51)
call_log_select_label.set_size(24, 12)
call_log_select_label.set_text("选择")
call_log_select_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
call_log_select_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
call_log_back_label = lv.label(call_log_screen)
call_log_back_label.set_pos(103, 51)
call_log_back_label.set_size(24, 12)
call_log_back_label.set_text("返回")
call_log_back_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
call_log_back_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
########################################################################
# description: 通话记录未接来电
# author: pawn
#########################################################################
missed_call_screen = lv.obj()
missed_call_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#
missed_call_list = lv.list(missed_call_screen)
missed_call_list.set_pos(0, 0)
missed_call_list.set_size(128, 48)
missed_call_list.set_style_pad_left(0, 0)
missed_call_list.set_style_pad_top(0, 1)
missed_call_list.set_style_pad_row(0, 0)
missed_call_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
missed_call_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
missed_call_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.SCROLLED)
# 底部功能栏
missed_call_options_label = lv.label(missed_call_screen)
missed_call_options_label.set_pos(0, 51)
missed_call_options_label.set_size(24, 12)
missed_call_options_label.set_text("选项")
missed_call_options_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
missed_call_options_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
missed_call_back_label = lv.label(missed_call_screen)
missed_call_back_label.set_pos(103, 51)
missed_call_back_label.set_size(24, 12)
missed_call_back_label.set_text("返回")
missed_call_back_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
missed_call_back_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
########################################################################
# description: 通话记录已接来电
# author: pawn
#########################################################################
received_call_screen = lv.obj()
received_call_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#
received_call_list = lv.list(received_call_screen)
received_call_list.set_pos(0, 0)
received_call_list.set_size(128, 48)
received_call_list.set_style_pad_left(0, 0)
received_call_list.set_style_pad_top(0, 1)
received_call_list.set_style_pad_row(0, 0)
received_call_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
received_call_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
received_call_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.SCROLLED)
# 底部功能栏
received_call_options_label = lv.label(received_call_screen)
received_call_options_label.set_pos(0, 51)
received_call_options_label.set_size(24, 12)
received_call_options_label.set_text("选项")
received_call_options_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
received_call_options_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
received_call_back_label = lv.label(received_call_screen)
received_call_back_label.set_pos(103, 51)
received_call_back_label.set_size(24, 12)
received_call_back_label.set_text("返回")
received_call_back_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
received_call_back_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
########################################################################
# description: 通话记录已拨来电
# author: pawn
#########################################################################
dialed_call_screen = lv.obj()
dialed_call_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#
dialed_call_list = lv.list(dialed_call_screen)
dialed_call_list.set_pos(0, 0)
dialed_call_list.set_size(128, 48)
dialed_call_list.set_style_pad_left(0, 0)
dialed_call_list.set_style_pad_top(0, 1)
dialed_call_list.set_style_pad_row(0, 0)
dialed_call_list.add_style(style_cont_white, lv.PART.MAIN | lv.STATE.DEFAULT)
dialed_call_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
dialed_call_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.SCROLLED)
# 底部功能栏
dialed_call_options_label = lv.label(dialed_call_screen)
dialed_call_options_label.set_pos(0, 51)
dialed_call_options_label.set_size(24, 12)
dialed_call_options_label.set_text("选项")
dialed_call_options_label.set_long_mode(lv.label.LONG.WRAP)
dialed_call_options_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
dialed_call_options_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
dialed_call_back_label = lv.label(dialed_call_screen)
dialed_call_back_label.set_pos(103, 51)
dialed_call_back_label.set_size(24, 12)
dialed_call_back_label.set_text("返回")
dialed_call_back_label.set_long_mode(lv.label.LONG.WRAP)
dialed_call_back_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
dialed_call_back_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
########################################################################
# description: 通话记录选项页
# author: pawn
#########################################################################
call_log_options_screen = lv.obj()
call_log_options_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#
call_log_options_list = lv.list(call_log_options_screen)
call_log_options_list.set_pos(0, 2)
call_log_options_list.set_size(128, 48)
call_log_options_list.set_style_pad_left(0, 0)
call_log_options_list.set_style_pad_top(0, 1)
call_log_options_list.set_style_pad_row(0, 0)
call_log_options_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
call_log_options_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
call_log_options_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.SCROLLED)
# 底部功能栏
call_log_options_ok_label = lv.label(call_log_options_screen)
call_log_options_ok_label.set_pos(0, 51)
call_log_options_ok_label.set_size(24, 12)
call_log_options_ok_label.set_text("确定")
call_log_options_ok_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
call_log_options_ok_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
call_log_options_back_label = lv.label(call_log_options_screen)
call_log_options_back_label.set_pos(103, 51)
call_log_options_back_label.set_size(24, 12)
call_log_options_back_label.set_text("返回")
call_log_options_back_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
call_log_options_back_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
########################################################################
# description: 通话记录选项详情页
# author: pawn
#########################################################################
call_log_options_details_screen = lv.obj()
call_log_options_details_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
# 创建文本框
call_log_options_details_viwe_screen = lv.obj(call_log_options_details_screen)
call_log_options_details_viwe_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
call_log_options_details_viwe_screen.set_style_pad_all(0, 0)
call_log_options_details_viwe_screen.set_pos(0, 2)
call_log_options_details_viwe_screen.set_size(128, 48)
# 文本标签
call_log_options_details_label = lv.label(call_log_options_details_viwe_screen)
call_log_options_details_label.set_pos(0, 0)
call_log_options_details_label.set_size(128, 48)
call_log_options_details_label.set_width(128)
call_log_options_details_label.set_height(lv.SIZE.CONTENT)
call_log_options_details_label.set_long_mode(lv.label.LONG.WRAP)
call_log_options_details_label.set_text("")
call_log_options_details_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
call_log_options_details_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
# 底部功能栏
# call_log_options_details_ok_label = lv.label(call_log_options_details_screen)
# call_log_options_details_ok_label.set_pos(0, 51)
# call_log_options_details_ok_label.set_size(24, 12)
# call_log_options_details_ok_label.set_text("选项")
# call_log_options_details_ok_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
# call_log_options_details_ok_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
call_log_options_details_back_label = lv.label(call_log_options_details_screen)
call_log_options_details_back_label.set_pos(103, 51)
call_log_options_details_back_label.set_size(24, 12)
call_log_options_details_back_label.set_text("返回")
call_log_options_details_back_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
call_log_options_details_back_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
########################################################################
# description: 信息页面
# author: pawn
#########################################################################
message_screen = lv.obj()
message_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)

message_list = lv.list(message_screen)
message_list.set_pos(0, 0)
message_list.set_size(128, 48)
message_list.set_style_pad_left(0, 0)
message_list.set_style_pad_top(0, 0)
message_list.set_style_pad_row(0, 0)
message_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
message_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR)
# 底部功能栏
message_select_label = lv.label(message_screen)
message_select_label.set_pos(0, 51)
message_select_label.set_size(24, 12)
message_select_label.set_text("选择")
message_select_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
message_select_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
message_back_label = lv.label(message_screen)
message_back_label.set_pos(103, 51)
message_back_label.set_size(24, 12)
message_back_label.set_text("返回")
message_back_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
message_back_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
########################################################################
# description: 写信息页面
# author: pawn
#########################################################################
new_message_screen = lv.obj()
new_message_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
# title
new_message_title_label = lv.label(new_message_screen)
new_message_title_label.set_pos(0, 0)
new_message_title_label.set_size(36, 12)
new_message_title_label.set_text("信息:")
new_message_title_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
new_message_title_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
# 输入法
new_message_input_label = lv.label(new_message_screen)
new_message_input_label.set_pos(50, 0)
new_message_input_label.set_size(24, 12)
new_message_input_label.set_text("")
new_message_input_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
new_message_input_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
# 当前输入字数以及最大数目
new_message_num_label = lv.label(new_message_screen)
new_message_num_label.set_pos(85, 0)
new_message_num_label.set_size(42, 12)
new_message_num_label.set_text("")
new_message_num_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
new_message_num_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
# 文本框对象
style_cursor = lv.style_t()
style_cursor.set_anim_time(1000)
new_message_viwe_screen = lv.obj(new_message_screen)
new_message_viwe_screen.set_pos(0, 13)
new_message_viwe_screen.set_size(128, 36)
new_message_viwe_screen.add_style(dark_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
new_message_viwe_screen.set_style_pad_all(0, 0)
new_message_text_screen = lv.textarea(new_message_viwe_screen)
new_message_text_screen.set_style_border_opa(0, 0)
new_message_text_screen.set_one_line(False)
new_message_text_screen.align(lv.ALIGN.TOP_MID, 0, 0)
new_message_text_screen.add_state(lv.STATE.FOCUSED)
new_message_text_screen.add_text('')
new_message_text_screen.set_pos(0, 0)
new_message_text_screen.set_size(128, 36)
new_message_text_screen.add_style(style_cursor, lv.PART.CURSOR | lv.STATE.FOCUSED)
new_message_text_screen.add_style(style_font_white_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#中文输入法
new_message_spell_viwe_screen = lv.obj(new_message_screen)
new_message_spell_viwe_screen.set_pos(0, 30)
new_message_spell_viwe_screen.set_size(200, 36)
new_message_spell_viwe_screen.set_style_pad_all(0, 0)
new_message_spell_viwe_screen.add_flag(1)
new_message_spell_viwe_screen.add_style(dark_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)

new_message_pinyin_ime = lv.ime_pinyin(new_message_spell_viwe_screen)
new_message_kb = lv.keyboard(new_message_spell_viwe_screen)

new_message_kb.set_textarea(new_message_text_screen)
new_message_pinyin_ime.pinyin_set_keyboard(new_message_kb)
new_message_pinyin_ime.pinyin_set_mode(lv.ime_pinyin.PINYIN_MODE.K9)
new_message_pinyin_ime.add_style(style_font_white_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
new_message_kb.add_flag(lv.obj.FLAG.HIDDEN)

new_message_structor = new_message_pinyin_ime.pinyin_get_cand_panel()
new_message_structor.set_size(128, 32)
new_message_structor.set_pos(0,10)
new_message_structor.add_style(style_font_white_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
# new_message_structor.get_selected_btn()
new_message_comb_panel = new_message_pinyin_ime.pinyin_get_comb_panel()
new_message_comb_panel.set_size(200,30)
new_message_comb_panel.set_pos(-35,-5)
new_message_comb_panel.add_style(style_font_white_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
# new_message_comb_panel.get_selected_btn()
# 底部功能栏
new_message_select_label = lv.label(new_message_screen)
new_message_select_label.set_pos(0, 51)
new_message_select_label.set_size(36, 12)
new_message_select_label.set_text("选项")
new_message_select_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
new_message_select_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
new_message_back_label = lv.label(new_message_screen)
new_message_back_label.set_pos(95, 51)
new_message_back_label.set_size(36, 12)
new_message_back_label.set_text("返回")
new_message_back_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
new_message_back_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
########################################################################
# description: 发送信息页面
# author: pawn
#########################################################################
send_message_screen = lv.obj()
send_message_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
# title
send_message_title_label = lv.label(send_message_screen)
send_message_title_label.set_pos(0, 0)
send_message_title_label.set_size(42, 12)
send_message_title_label.set_text("收件人:")
send_message_title_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
send_message_title_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
# 输入法
send_message_input_label = lv.label(send_message_screen)
send_message_input_label.set_pos(102, 0)
send_message_input_label.set_size(24, 12)
send_message_input_label.set_text("123")
send_message_input_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
send_message_input_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
# 文本框对象
send_message_viwe_screen = lv.obj(send_message_screen)
send_message_viwe_screen.set_pos(0, 13)
send_message_viwe_screen.set_size(128, 36)
send_message_viwe_screen.add_style(dark_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
send_message_viwe_screen.set_style_pad_all(0, 0)

send_message_text_screen = lv.textarea(send_message_viwe_screen)
send_message_text_screen.set_style_border_opa(0, 0)
send_message_text_screen.set_one_line(False)
send_message_text_screen.align(lv.ALIGN.TOP_MID, 0, 0)
send_message_text_screen.add_state(lv.STATE.FOCUSED)
send_message_text_screen.add_text('')
send_message_text_screen.set_pos(0, 0)
send_message_text_screen.set_size(128, 36)
send_message_text_screen.add_style(style_font_white_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
# 底部功能栏
send_message_select_label = lv.label(send_message_screen)
send_message_select_label.set_pos(0, 51)
send_message_select_label.set_size(24, 12)
send_message_select_label.set_text("选项")
send_message_select_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
send_message_select_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
send_message_back_label = lv.label(send_message_screen)
send_message_back_label.set_pos(103, 51)
send_message_back_label.set_size(24, 12)
send_message_back_label.set_text("返回")
send_message_back_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
send_message_back_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
########################################################################
# description: 标点符号页面
# author: pawn
#########################################################################
symbol_screen = lv.obj()
symbol_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
symbol_viwe_screen = lv.obj(symbol_screen)
symbol_viwe_screen.set_pos(0, 0)
symbol_viwe_screen.set_size(128, 50)
symbol_viwe_screen.add_style(dark_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
symbol_viwe_screen.set_style_pad_all(0, 0)
symbol_btnm = lv.btnmatrix(symbol_viwe_screen)
symbol_btnm.set_pos(0, 1)
symbol_btnm.set_size(128, 63)
symbol_btnm.add_style(style_font_white_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
symbol_btnm.set_btn_ctrl_all(lv.btnmatrix.CTRL.CHECKABLE)
symbol_btnm.set_one_checked(True)
# 底部功能栏
symbol_select_label = lv.label(symbol_screen)
symbol_select_label.set_pos(0, 51)
symbol_select_label.set_size(24, 12)
symbol_select_label.set_text("选择")
symbol_select_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
symbol_select_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
symbol_back_label = lv.label(symbol_screen)
symbol_back_label.set_pos(103, 51)
symbol_back_label.set_size(24, 12)
symbol_back_label.set_text("返回")
symbol_back_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
symbol_back_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
########################################################################
# description: 发送信息选项页面
# author: pawn
#########################################################################
send_msg_option_screen = lv.obj()
send_msg_option_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)

send_msg_option_list = lv.list(send_msg_option_screen)
send_msg_option_list.set_pos(0, 0)
send_msg_option_list.set_size(128, 48)
send_msg_option_list.set_style_pad_left(0, 0)
send_msg_option_list.set_style_pad_top(0, 0)
send_msg_option_list.set_style_pad_row(0, 0)
send_msg_option_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
send_msg_option_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR)
# 底部功能栏
send_msg_option_select_label = lv.label(send_msg_option_screen)
send_msg_option_select_label.set_pos(0, 51)
send_msg_option_select_label.set_size(24, 12)
send_msg_option_select_label.set_text("选择")
send_msg_option_select_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
send_msg_option_select_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
send_msg_option_back_label = lv.label(send_msg_option_screen)
send_msg_option_back_label.set_pos(103, 51)
send_msg_option_back_label.set_size(24, 12)
send_msg_option_back_label.set_text("返回")
send_msg_option_back_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
send_msg_option_back_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)

########################################################################
# description: 收件箱页面
# author: pawn
#########################################################################
inbox_screen = lv.obj()
inbox_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
# 信息为空显示
inbox_null = lv.label(inbox_screen)
# inbox_null.set_text("<空>")
inbox_null.set_text("")
inbox_null.center()
inbox_null.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
# 信息列表
inbox_list = lv.list(inbox_screen)
inbox_list.set_pos(0, 0)
inbox_list.set_size(128, 48)
inbox_list.set_style_pad_left(0, 0)
inbox_list.set_style_pad_top(0, 0)
inbox_list.set_style_pad_row(2, 0)
inbox_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
# 底部功能栏
inbox_select_label = lv.label(inbox_screen)
inbox_select_label.set_pos(0, 51)
inbox_select_label.set_size(24, 12)
inbox_select_label.set_text("选项")
inbox_select_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
inbox_select_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#显示内存数
inbox_screen_memory_value = lv.label(inbox_screen)
inbox_screen_memory_value.set_pos(48, 51)
inbox_screen_memory_value.set_size(48, 12)
inbox_screen_memory_value.set_text("")
inbox_screen_memory_value.set_long_mode(lv.label.LONG.WRAP)
inbox_screen_memory_value.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
inbox_screen_memory_value.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
 #底部功能栏
inbox_back_label = lv.label(inbox_screen)
inbox_back_label.set_pos(103, 51)
inbox_back_label.set_size(24, 12)
inbox_back_label.set_text("返回")
inbox_back_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
inbox_back_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
########################################################################
# description: 收件箱选项页面
# author: pawn
#########################################################################
inbox_option_screen = lv.obj()
inbox_option_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)

inbox_option_list = lv.list(inbox_option_screen)
inbox_option_list.set_pos(0, 0)
inbox_option_list.set_size(128, 48)
inbox_option_list.set_style_pad_left(0, 0)
inbox_option_list.set_style_pad_top(0, 0)
inbox_option_list.set_style_pad_row(0, 0)
inbox_option_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
inbox_option_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR)
# 底部功能栏
inbox_option_select_label = lv.label(inbox_option_screen)
inbox_option_select_label.set_pos(0, 51)
inbox_option_select_label.set_size(24, 12)
inbox_option_select_label.set_text("选择")
inbox_option_select_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
inbox_option_select_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
inbox_option_back_label = lv.label(inbox_option_screen)
inbox_option_back_label.set_pos(103, 51)
inbox_option_back_label.set_size(24, 12)
inbox_option_back_label.set_text("返回")
inbox_option_back_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
inbox_option_back_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
########################################################################
# description: 信息选项查看详情页
# author: pawn
#########################################################################
message_option_details_screen = lv.obj()
message_option_details_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
# 创建文本框
message_option_details_viwe_screen = lv.obj(message_option_details_screen)
message_option_details_viwe_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
message_option_details_viwe_screen.set_style_pad_all(0, 0)
message_option_details_viwe_screen.set_pos(0, 3)
message_option_details_viwe_screen.set_size(128, 48)
# 文本标签
message_option_details_label = lv.label(message_option_details_viwe_screen)
message_option_details_label.set_pos(0, 0)
message_option_details_label.set_size(128, 48)
message_option_details_label.set_width(128)
message_option_details_label.set_height(lv.SIZE.CONTENT)
message_option_details_label.set_long_mode(lv.label.LONG.WRAP)
message_option_details_label.set_text("")
message_option_details_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
message_option_details_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
# 底部功能栏
message_option_details_back_label = lv.label(message_option_details_screen)
message_option_details_back_label.set_pos(103, 51)
message_option_details_back_label.set_size(24, 12)
message_option_details_back_label.set_text("返回")
message_option_details_back_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
message_option_details_back_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
########################################################################
# description: 发件箱页面
# author: pawn
#########################################################################
outbox_screen = lv.obj()
outbox_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
# 信息为空显示
outbox_null = lv.label(outbox_screen)
# outbox_null.set_text("<空>")
outbox_null.set_text("")
outbox_null.center()
outbox_null.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
# 信息列表
outbox_list = lv.list(outbox_screen)
outbox_list.set_pos(0, 0)
outbox_list.set_size(128, 48)
outbox_list.set_style_pad_left(0, 0)
outbox_list.set_style_pad_top(0, 0)
outbox_list.set_style_pad_row(2, 0)
outbox_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
# 底部功能栏
outbox_select_label = lv.label(outbox_screen)
outbox_select_label.set_pos(0, 51)
outbox_select_label.set_size(54, 12)
outbox_select_label.set_text("选项")
outbox_select_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
outbox_select_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#显示内存数
outbox_screen_memory_value = lv.label(outbox_screen)
outbox_screen_memory_value.set_pos(48, 51)
outbox_screen_memory_value.set_size(48, 12)
outbox_screen_memory_value.set_text("")
outbox_screen_memory_value.set_long_mode(lv.label.LONG.WRAP)
outbox_screen_memory_value.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
outbox_screen_memory_value.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
 #底部功能栏
outbox_back_label = lv.label(outbox_screen)
outbox_back_label.set_pos(103, 51)
outbox_back_label.set_size(24, 12)
outbox_back_label.set_text("返回")
outbox_back_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
outbox_back_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
########################################################################
# description: 发件箱选项页面
# author: pawn
#########################################################################
outbox_option_screen = lv.obj()
outbox_option_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)

outbox_option_list = lv.list(outbox_option_screen)
outbox_option_list.set_pos(0, 0)
outbox_option_list.set_size(128, 48)
outbox_option_list.set_style_pad_left(0, 0)
outbox_option_list.set_style_pad_top(0, 0)
outbox_option_list.set_style_pad_row(0, 0)
outbox_option_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
outbox_option_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR)
# 底部功能栏
outbox_option_select_label = lv.label(outbox_option_screen)
outbox_option_select_label.set_pos(0, 51)
outbox_option_select_label.set_size(54, 12)
outbox_option_select_label.set_text("选择")
outbox_option_select_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
outbox_option_select_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
outbox_option_back_label = lv.label(outbox_option_screen)
outbox_option_back_label.set_pos(102, 51)
outbox_option_back_label.set_size(45, 12)
outbox_option_back_label.set_text("返回")
outbox_option_back_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
outbox_option_back_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
########################################################################
common_phrases_screen = lv.obj()
common_phrases_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#话机设置列表
common_phrases_list = lv.list(common_phrases_screen)
common_phrases_list.set_pos(0, 0)
common_phrases_list.set_size(128, 48)
common_phrases_list.set_style_pad_left(0,0)
common_phrases_list.set_style_pad_top(0, 0)
common_phrases_list.set_style_pad_row(0, 0)
common_phrases_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
common_phrases_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
#设置页面确认按钮样式
common_phrases_sure = lv.label(common_phrases_screen)
common_phrases_sure.set_pos(0, 51)
common_phrases_sure.set_size(24, 12)
common_phrases_sure.set_text("Edit")
common_phrases_sure.set_long_mode(lv.label.LONG.WRAP)
common_phrases_sure.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
# add style for common_phrases_sure
common_phrases_sure.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#设置页面返回按钮样式
common_phrases_return = lv.label(common_phrases_screen)
common_phrases_return.set_pos(103, 51)
common_phrases_return.set_size(24, 12)
common_phrases_return.set_text("Back")
common_phrases_return.set_long_mode(lv.label.LONG.WRAP)
common_phrases_return.set_style_text_align(lv.TEXT_ALIGN.RIGHT, 0)
common_phrases_return.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
########################################################################
######草稿箱页面
########################################################################
drafts_box_screen = lv.obj()
drafts_box_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#话机设置列表
drafts_box_list = lv.list(drafts_box_screen)
drafts_box_list.set_pos(0, 0)
drafts_box_list.set_size(128, 48)
drafts_box_list.set_style_pad_left(0,0)
drafts_box_list.set_style_pad_top(0, 0)
drafts_box_list.set_style_pad_row(0, 0)
drafts_box_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
drafts_box_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
#设置页面确认按钮样式
drafts_box_sure = lv.label(drafts_box_screen)
drafts_box_sure.set_pos(0, 51)
drafts_box_sure.set_size(36, 12)
drafts_box_sure.set_text("Select")
drafts_box_sure.set_long_mode(lv.label.LONG.WRAP)
drafts_box_sure.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
# add style for common_phrases_sure
drafts_box_sure.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
# drafts_box_sure.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#显示内存数
drafts_box_memory_value = lv.label(drafts_box_screen)
drafts_box_memory_value.set_pos(48, 51)
drafts_box_memory_value.set_size(48, 12)
drafts_box_memory_value.set_text("")
drafts_box_memory_value.set_long_mode(lv.label.LONG.WRAP)
drafts_box_memory_value.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
drafts_box_memory_value.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#设置页面返回按钮样式
drafts_box_return = lv.label(drafts_box_screen)
drafts_box_return.set_pos(103, 51)
drafts_box_return.set_size(24, 12)
drafts_box_return.set_text("Back")
drafts_box_return.set_long_mode(lv.label.LONG.WRAP)
drafts_box_return.set_style_text_align(lv.TEXT_ALIGN.RIGHT, 0)
drafts_box_return.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
########################################################################
########################################################################
#草稿箱选项界面
########################################################################
drafts_send_option_screen = lv.obj()
drafts_send_option_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#话机设置列表
drafts_send_option_list = lv.list(drafts_send_option_screen)
drafts_send_option_list.set_pos(0, 0)
drafts_send_option_list.set_size(128, 48)
drafts_send_option_list.set_style_pad_left(0,0)
drafts_send_option_list.set_style_pad_top(0, 0)
drafts_send_option_list.set_style_pad_row(0, 0)
drafts_send_option_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
drafts_send_option_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
#设置页面确认按钮样式
drafts_send_option_sure = lv.label(drafts_send_option_screen)
drafts_send_option_sure.set_pos(0, 51)
drafts_send_option_sure.set_size(36, 12)
drafts_send_option_sure.set_text("Select")
drafts_send_option_sure.set_long_mode(lv.label.LONG.WRAP)
drafts_send_option_sure.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
# add style for send_message_option_sure
drafts_send_option_sure.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#设置页面返回按钮样式
drafts_send_option_return = lv.label(drafts_send_option_screen)
drafts_send_option_return.set_pos(103, 51)
drafts_send_option_return.set_size(24, 12)
drafts_send_option_return.set_text("Back")
drafts_send_option_return.set_long_mode(lv.label.LONG.WRAP)
drafts_send_option_return.set_style_text_align(lv.TEXT_ALIGN.RIGHT, 0)
drafts_send_option_return.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
########################################################################
########################################################################
# description: 草稿箱发送后删除提示界面
# author: pawn
#########################################################################
drafts_del_screen = lv.obj()
drafts_del_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
drafts_del_screen_label = lv.label(drafts_del_screen)
drafts_del_screen_label.set_pos(12, 26)
drafts_del_screen_label.set_size(128, 52)
drafts_del_screen_label.set_text("删除该草稿?")
drafts_del_screen_label.set_long_mode(lv.label.LONG.WRAP)
drafts_del_screen_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)#删除列表
#删除页面确认按钮样式
drafts_del_screen_sure = lv.label(drafts_del_screen)
drafts_del_screen_sure.set_pos(0, 51)
drafts_del_screen_sure.set_size(24, 12)
drafts_del_screen_sure.set_text("确定")
drafts_del_screen_sure.set_long_mode(lv.label.LONG.WRAP)
drafts_del_screen_sure.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
# add style for networkservice_list_sure
drafts_del_screen_sure.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#删除页面取消按钮样式
drafts_del_screen_return = lv.label(drafts_del_screen)
drafts_del_screen_return.set_pos(103, 51)
drafts_del_screen_return.set_size(24, 12)
drafts_del_screen_return.set_text("取消")
drafts_del_screen_return.set_long_mode(lv.label.LONG.WRAP)
drafts_del_screen_return.set_style_text_align(lv.TEXT_ALIGN.RIGHT, 0)
drafts_del_screen_return.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
########################################################################
send_message_option_screen = lv.obj()
send_message_option_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#话机设置列表
send_message_option_list = lv.list(send_message_option_screen)
send_message_option_list.set_pos(0, 0)
send_message_option_list.set_size(128, 48)
send_message_option_list.set_style_pad_left(0,0)
send_message_option_list.set_style_pad_top(0, 0)
send_message_option_list.set_style_pad_row(0, 0)
send_message_option_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
send_message_option_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
#设置页面确认按钮样式
send_message_option_sure = lv.label(send_message_option_screen)
send_message_option_sure.set_pos(0, 51)
send_message_option_sure.set_size(24, 12)
send_message_option_sure.set_text("确定")
send_message_option_sure.set_long_mode(lv.label.LONG.WRAP)
send_message_option_sure.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
# add style for send_message_option_sure
send_message_option_sure.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#设置页面返回按钮样式
send_message_option_return = lv.label(send_message_option_screen)
send_message_option_return.set_pos(103, 51)
send_message_option_return.set_size(24, 12)
send_message_option_return.set_text("返回")
send_message_option_return.set_long_mode(lv.label.LONG.WRAP)
send_message_option_return.set_style_text_align(lv.TEXT_ALIGN.RIGHT, 0)
send_message_option_return.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
########################################################################
# description: 电话本删除提示界面
# author: pawn
#########################################################################
phonebook_delete_screen = lv.obj()
phonebook_delete_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
phonebook_delete_screen_label = lv.label(phonebook_delete_screen)
phonebook_delete_screen_label.set_pos(30, 26)
phonebook_delete_screen_label.set_size(128, 52)
phonebook_delete_screen_label.set_text("确认删除吗?")
phonebook_delete_screen_label.set_long_mode(lv.label.LONG.WRAP)
phonebook_delete_screen_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)#删除列表
#删除页面确认按钮样式
phonebook_delete_screen_sure = lv.label(phonebook_delete_screen)
phonebook_delete_screen_sure.set_pos(0, 51)
phonebook_delete_screen_sure.set_size(24, 12)
phonebook_delete_screen_sure.set_text("确定")
phonebook_delete_screen_sure.set_long_mode(lv.label.LONG.WRAP)
phonebook_delete_screen_sure.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
# add style for networkservice_list_sure
phonebook_delete_screen_sure.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#删除页面取消按钮样式
phonebook_delete_screen_return = lv.label(phonebook_delete_screen)
phonebook_delete_screen_return.set_pos(103, 51)
phonebook_delete_screen_return.set_size(24, 12)
phonebook_delete_screen_return.set_text("取消")
phonebook_delete_screen_return.set_long_mode(lv.label.LONG.WRAP)
phonebook_delete_screen_return.set_style_text_align(lv.TEXT_ALIGN.RIGHT, 0)
phonebook_delete_screen_return.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
######################################################################
#拨号选项页面
dial_options_screen = lv.obj()
dial_options_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#
dial_options_list = lv.list(dial_options_screen)
dial_options_list.set_pos(0, 0)
dial_options_list.set_size(128, 36)
dial_options_list.set_style_pad_left(0, 0)
dial_options_list.set_style_pad_top(0, 0)
dial_options_list.set_style_pad_row(0, 0)
dial_options_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
dial_options_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
dial_options_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.SCROLLED)
# 底部功能栏
dial_options_select_label = lv.label(dial_options_screen)
dial_options_select_label.set_pos(0, 51)
dial_options_select_label.set_size(24, 12)
dial_options_select_label.set_text("选择")
dial_options_select_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
dial_options_select_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
dial_options_back_label = lv.label(dial_options_screen)
dial_options_back_label.set_pos(103, 51)
dial_options_back_label.set_size(24, 12)
dial_options_back_label.set_text("返回")
dial_options_back_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
dial_options_back_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
############################LAN界面
ethernet_lan_screen = lv.obj()
ethernet_lan_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#LAN界面标题
ethernet_lan_title = lv.label(ethernet_lan_screen)
ethernet_lan_title.set_pos(0, 0)
ethernet_lan_title.set_size(128, 52)
ethernet_lan_title.set_text("Lan配置")
ethernet_lan_title.set_long_mode(lv.label.LONG.WRAP)
ethernet_lan_title.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
ethernet_lan_title.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#LAN界面样式
ethernet_lan_list = lv.list(ethernet_lan_screen)
ethernet_lan_list.set_pos(0, 16)
ethernet_lan_list.set_size(128, 16)
ethernet_lan_list.set_style_pad_left(0, 0)
ethernet_lan_list.set_style_pad_top(0, 1)
ethernet_lan_list.set_style_pad_row(0, 0)
ethernet_lan_list.add_style(style_cont_white, lv.PART.MAIN | lv.STATE.DEFAULT)
ethernet_lan_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
ethernet_lan_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.SCROLLED)
#LAN选择
ethernet_lan_sure = lv.label(ethernet_lan_screen)
ethernet_lan_sure.set_pos(0, 51)
ethernet_lan_sure.set_size(48, 12)
ethernet_lan_sure.set_text("选择")
ethernet_lan_sure.set_long_mode(lv.label.LONG.WRAP)
ethernet_lan_sure.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
ethernet_lan_sure.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#LAN返回
ethernet_lan_return = lv.label(ethernet_lan_screen)
ethernet_lan_return.set_pos(103, 51)
ethernet_lan_return.set_size(24, 12)
ethernet_lan_return.set_text("返回")
ethernet_lan_return.set_long_mode(lv.label.LONG.WRAP)
ethernet_lan_return.set_style_text_align(lv.TEXT_ALIGN.RIGHT, 0)
ethernet_lan_return.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)

############################以太网配置详情界面
Ethernet_detail_screen = lv.obj()
Ethernet_detail_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#以太网配置详情界面标题
# Ethernet_detail_title = lv.label(Ethernet_detail_screen)
# Ethernet_detail_title.set_pos(0, 0)
# Ethernet_detail_title.set_size(128, 52)
# Ethernet_detail_title.set_text("Details")
# Ethernet_detail_title.set_long_mode(lv.label.LONG.WRAP)
# Ethernet_detail_title.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
# Ethernet_detail_title.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
#以太网配置界面样式
Ethernet_detail_list = lv.list(Ethernet_detail_screen)
Ethernet_detail_list.set_pos(0, 32)
Ethernet_detail_list.set_size(128, 12)
Ethernet_detail_list.set_style_pad_left(0, 0)
Ethernet_detail_list.set_style_pad_top(0, 1)
Ethernet_detail_list.set_style_pad_row(0, 0)

Ethernet_detail_list.add_style(style_cont_white, lv.PART.MAIN | lv.STATE.DEFAULT)
Ethernet_detail_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.DEFAULT)
Ethernet_detail_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.SCROLLED)
#以太网配置 返回
Ethernet_detail_return = lv.label(Ethernet_detail_screen)
Ethernet_detail_return.set_pos(103, 51)
Ethernet_detail_return.set_size(24, 12)
Ethernet_detail_return.set_text("返回")
Ethernet_detail_return.set_long_mode(lv.label.LONG.WRAP)
Ethernet_detail_return.set_style_text_align(lv.TEXT_ALIGN.RIGHT, 0)
Ethernet_detail_return.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)

############################WiFi配置详情界面
WiFi_detail_screen = lv.obj()
WiFi_detail_screen.add_style(light_color_style_screen, lv.PART.MAIN | lv.STATE.DEFAULT)
#wifi配置 返回
WiFi_detail_return = lv.label(WiFi_detail_screen)
WiFi_detail_return.set_pos(103, 51)
WiFi_detail_return.set_size(24, 12)
WiFi_detail_return.set_text("返回")
WiFi_detail_return.set_long_mode(lv.label.LONG.WRAP)
WiFi_detail_return.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
WiFi_detail_return.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)

WiFi_detail_SSID = lv.label(WiFi_detail_screen)
WiFi_detail_SSID.set_pos(0, 16)
WiFi_detail_SSID.set_size(128, 12)
WiFi_detail_SSID.set_text("")
WiFi_detail_SSID.set_long_mode(lv.label.LONG.WRAP)
WiFi_detail_SSID.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
WiFi_detail_SSID.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)

WiFi_detail_password = lv.label(WiFi_detail_screen)
WiFi_detail_password.set_pos(0, 32)
WiFi_detail_password.set_size(128, 12)
WiFi_detail_password.set_text("")
WiFi_detail_password.set_long_mode(lv.label.LONG.WRAP)
WiFi_detail_password.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
WiFi_detail_password.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)


class Screen(Abstract):
    """
    screen
        继承于Abstract
        既支持生命周期功能

    """
    def __init__(self):
        """
            meta 代表页面组件lv.obj()对象的
            meta_info 代表从A跳转到B界面, A想通知一些参数给B界面时携带
            pre_screen 类似于浏览器中上一界面功能, 这里只是做记录, 因为A跳B, B在跳回来会造成循环的pre_screen, 但是有些情况下我们会用到这一个参数, 例如获取上一个界面的信息等
        """
        self.meta = None
        self.meta_info = dict()
        self.pre_screen = None

    def set_meta_info(self, meta_info):
        self.meta_info = meta_info

    def set_pre_screen(self, pre_screen):
        self.pre_screen = pre_screen

    ##  下面需要定义的事界面支持的所有按键事件这里只是举例, 必须所有事件都在这里有个默认实现, 否则, 当C界面没有默认实现的话, 我们浏览器就是我们后面的UI类触发调用, 会导致调用不到出错的 ##
    def btn_menu_click(self):
        pass

    def btn_back_click(self):
        pass

    def btn_num_click(self, msg):
        pass

    def btn_symbol_click(self, msg):
        pass

    def btn_up_click(self):
        pass

    def btn_down_click(self):
        pass

    def btn_left_click(self):
        pass

    def btn_right_click(self):
        pass

    def btn_fast_1_click(self):
        pass

    def btn_fast_2_click(self):
        pass

    def btn_fast_3_click(self):
        pass

    def btn_fast_4_click(self):
        pass

    def btn_hands_free_click(self,status):
        pass

    def btn_headset_click(self):
        pass

    def btn_redial_click(self):
        pass

    def btn_call_click(self):
        pass

    def btn_pwk_click(self):
        pass

    def btn_pwk_long_click(self):
        pass

    def btn_num_long_click(self,msg):
        pass

    def btn_fast_click(self,No):
        pass

    def btn_fast_long_click(self,msg):
        pass

    def btn_hook_click(self,status):
        pass

    def prev_idx(self, now_idx, count):
        cur_idx = now_idx - 1
        if cur_idx < 0:
            cur_idx = count - 1
        return cur_idx

    def next_idx(self, now_idx, count):
        cur_idx = now_idx + 1
        if cur_idx > count - 1:
            cur_idx = 0
        return cur_idx

    @staticmethod
    def publish_time():
        # 主动向后端请求时间
        return EventMesh.publish("screen_get_time")

class PopUpWindow(Abstract):
    '''弹窗'''

    def __init__(self):
        super().__init__()
        self.window_new_flag = False
        self.window_box = None
        self.window_label = None
        self.window_box_timer = osTimer()

    def post_processor_after_instantiation(self):
        EventMesh.subscribe("window_show", self.show)
        EventMesh.subscribe("window_hide", self.hide)
        EventMesh.subscribe("get_window_flag", self.get_window_new_flag)

    def get_window_new_flag(self, topic=None, data=None):
        return self.window_new_flag

    def hide(self, *args):
        if self.window_box:
            self.window_box_timer.stop()
            self.window_box.delete()
            self.window_box = None
            self.window_new_flag = False

    def show(self, topic, data=None):
        if data is None:
            return False
        if self.window_box:
            self.window_box.delete()
            self.window_new_flag = False
        self.window_box = lv.win(lv.scr_act(), 0)
        # 设置位置
        self.window_box.set_size(112, 50)
        # 设置圆角
        self.window_box.set_style_radius(10, 0)
        self.window_box.center()
        self.cont = self.window_box.get_content()

        self.window_label = lv.label(self.cont)
        self.window_label.set_text(data)
        self.window_label.center()
        # 设置自动换行
        self.window_label.set_width(112)
        self.window_label.set_long_mode(lv.label.LONG.WRAP)
        self.window_label.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
        self.window_label.add_style(style_font_white_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)

        self.window_new_flag = True
        # 启动一个定时器，2秒后主动关闭弹窗
        self.window_box_timer.start(2000, 0, self.hide)

class WelcomeScreen(Screen):
    """欢迎页面"""
    NAME = "welcome"

    def __init__(self):
        super().__init__()
        self.meta = bye_screen
        self.bye_label = bye_label

    def initialization(self):
        if self.meta_info == 1:
            print("WelcomeScreen:Byte Byte")
            self.bye_label.set_text("GoodBye")
        return True

class ChargingScreen(Screen):
    """欢迎页面"""
    NAME = "charging"

    def __init__(self):
        super().__init__()
        self.enter_main = 0
        self.charging_idx = 0
        self.tempture_state = 0
        self.meta = charging_screen
        self.charging_screen_img = charging_screen_img
        self.charging_timer = osTimer()
        self.wif_timer = osTimer()
        self.charging_timer_stop = 0
        self.charging_path = [
            "U:/static/Charging1.png",
            "U:/static/Charging2.png",
            "U:/static/Charging3.png",
            "U:/static/Charging4.png",
            "U:/static/Charging5.png"
        ]
        self.abnormal_charing = "U:/static/Charging0.png"

    def post_processor_after_instantiation(self):
        EventMesh.subscribe("get_IsgoTomain", self.isMain)
        EventMesh.subscribe("set_goTomain", self.enterMain)
        EventMesh.subscribe("enter_main_screen", self.enter_main_screen)

    def enterMain(self,topic=None, data=None):
        if self.charging_timer_stop == 0:
            self.charging_timer.stop()
        self.charging_timer_stop = 1
        self.enter_main = 1

    def isMain(self,topic=None, data=None):
        return self.enter_main

    def initialization(self):
        self.charging_idx = 0
        self.charging_timer_stop = 0
        EventMesh.publish("backlight_control","1")
        self.charging_timer.start(1000, 1, self.display_charging_timer)
        return True

    def display_charging_timer(self,*args):
        if EventMesh.publish("get_charging_state") == 1:
            # 插电源检测温度是否合法
            if EventMesh.publish("get_tempture_valid") == 1:
                # 温度不合法停止充电显示当前电池百分比
                self.poweroff_battery_display()
            else:
                #工作正常显示充电符号
                if EventMesh.publish("get_charging_full_state") == 1:
                    #电池充满电视充满符号
                    self.charging_screen_img.set_src(self.charging_path[4])
                else:
                    self.charging_idx = self.charging_idx + 1
                    if self.charging_idx >= 5:
                        self.charging_idx = 0
                    self.charging_screen_img.set_src(self.charging_path[self.charging_idx])
                return
        else:  # 非充电状态
            if EventMesh.publish("get_tempture_valid") == 1:
                self.charging_screen_img.set_src(self.abnormal_charing)
            else: 
                self.poweroff_battery_display()
            # self.charging_timer.stop()
            # self.charging_timer_stop = 1
            # Power.powerDown()
            return

    def poweroff_battery_display(self):
        bat_num = EventMesh.publish("get_battery_energy")
        # print("ChargingScreen get_battery bat_num:",bat_num)
        EventMesh.publish("uart_log","ChargingScreen get_battery bat_num:"+str(bat_num))
        battery_level = int(bat_num / 20)
        if battery_level >= 5:
            battery_level = 4
        if battery_level < 0:
            battery_level = 0
        self.charging_screen_img.set_src(self.charging_path[battery_level])

    def btn_pwk_click(self):
        if EventMesh.publish("backlight_get_state") == 1:
            EventMesh.publish("backlight_control","0")
        else:
            EventMesh.publish("backlight_control","1")
        return True

    def btn_pwk_long_click(self):
        EventMesh.publish("enter_main_screen")
        # EventMesh.publish("set_next_alarm")

    def enter_main_screen(self,topic=None,msg=None):
        self.charging_timer_stop=1
        self.charging_timer.stop()
        self.enter_main = 1
        net.setModemFun(1)
        
        POWER_gpio = Pin(Pin.GPIO38 , Pin.OUT, Pin.PULL_DISABLE, 1)
        POWER_gpio.write(1)
        
        EventMesh.publish("load_screen", {"screen": "main"})
        EventMesh.publish("restart_simcard_status_timer")

################################################################
#                           安全设置菜单
################################################################
#PIN码管理界面
class PinManageScreen(Screen):

    NAME = "pin"
    def __init__(self):
        super().__init__()
        self.meta = pinmgr_screen
        self.cur = 0
        self.count = 3
        self.pin_enable = ""
        self.pinmgr_setting_list = pinmgr_screen_list
        self.pinmgr_screen_title = pinmgr_screen_title
        self.pinmgr_screen_value = pinmgr_screen_value
        self.pinmgr_screen_sure = pinmgr_screen_sure
        self.pinmgr_screen_return = pinmgr_screen_return
        self.btn_list = []
        self.language = "zh"
        self.currentButton = None
        self.pinmgr_menu_list = [
            {
                "screen": "keypad_input",
                "title":" 1 关闭PIN",
            },
            {
                "screen": "keypad_input",
                "title":" 2 开启PIN",
            },
            {
                "screen": "keypad_input",
                "title":" 3 更改PIN",
            },
        ]
        self.pinmgr_menu_list_en = [
            {
                "screen": "keypad_input",
                "title":" 1 Close PIN",
            },
            {
                "screen": "keypad_input",
                "title":" 2 Open PIN",
            },
            {
                "screen": "keypad_input",
                "title":" 3 Change PIN",
            },
        ]

    def initialization(self):
        self.btn_list = []
        self.list_create()
        return True

    def list_create(self):
        self.pinmgr_setting_list.delete()
        self.pinmgr_setting_list = lv.list(self.meta)
        self.pinmgr_setting_list.set_pos(0, 16)
        self.pinmgr_setting_list.set_size(128, 32)
        self.pinmgr_setting_list.set_style_pad_left(0, 0)
        self.pinmgr_setting_list.set_style_pad_top(0, 1)
        self.pinmgr_setting_list.set_style_pad_row(0, 0)
        self.pinmgr_setting_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
        self.pinmgr_setting_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR)
        self.language = EventMesh.publish("persistent_config_get", "language")
        if self.language == "zh":
            self.option=self.pinmgr_menu_list
            self.pinmgr_screen_title.set_text("PIN码管理")
            self.pinmgr_screen_value.set_text("关闭")
            self.pinmgr_screen_sure.set_text("选择")
            self.pinmgr_screen_return.set_text("返回")
        else:
            self.option=self.pinmgr_menu_list_en
            self.pinmgr_screen_title.set_text("PIN Manage")
            self.pinmgr_screen_value.set_text("Close")
            self.pinmgr_screen_sure.set_text("Select")
            self.pinmgr_screen_return.set_text("Back")
        for idx, item in enumerate(self.option):
            pinmgr_obj_list_btn = lv.btn(self.pinmgr_setting_list)
            pinmgr_obj_list_btn.set_pos(0, 1)
            pinmgr_obj_list_btn.set_size(115, 16)
            pinmgr_obj_list_btn.add_style(style_list_black, lv.PART.MAIN | lv.STATE.DEFAULT)
            pinmgr_obj_list_label = lv.label(pinmgr_obj_list_btn)
            pinmgr_obj_list_label.center()
            pinmgr_obj_list_label.set_pos(0, 2)
            pinmgr_obj_list_label.set_size(115, 16)
            pinmgr_obj_list_label.set_text(item["title"])
            pinmgr_obj_list_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
            self.btn_list.append((pinmgr_obj_list_btn, pinmgr_obj_list_label))

        self.add_state()

    def add_state(self, cur=None):
        self.language = EventMesh.publish("persistent_config_get", "language")
        if cur is None:
            cur = self.cur
        self.pin_enable = EventMesh.publish("persistent_config_get","pin")
        if self.pin_enable == None:
            self.pin_enable = "0"

        if EventMesh.publish("about_get_pin_status") == 1:
            print("pin开启")
            if self.language == "zh":
                self.pinmgr_screen_value.set_text("开启")
            else:
                self.pinmgr_screen_value.set_text("Open")
            if self.pin_enable == "0":
                EventMesh.publish("persistent_config_store",{"pin": "1"})
        else:
            if self.language == "zh":
                self.pinmgr_screen_value.set_text("关闭")
            else:
                self.pinmgr_screen_value.set_size(48, 12)
                self.pinmgr_screen_value.set_text("Close")
            if self.pin_enable == "1":
                EventMesh.publish("persistent_config_store",{"pin": "0"})
        self.currentButton = self.pinmgr_setting_list.get_child(cur)
        self.currentButton.set_style_bg_color(lv.color_make(0xff, 0xff, 0xff), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.set_style_bg_grad_color(lv.color_make(0xff, 0xff, 0xff), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.btn_list[cur][1].set_style_text_color(lv.color_make(0x00, 0x00, 0x00), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.scroll_to_view(lv.ANIM.OFF)

    def clear_state(self, cur=None):
        if cur is None:
            cur = self.cur
        self.currentButton = self.pinmgr_setting_list.get_child(cur)
        self.currentButton.set_style_bg_color(lv.color_make(0x00, 0x00, 0x00), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.set_style_bg_grad_color(lv.color_make(0x00, 0x00, 0x00), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.btn_list[cur][1].set_style_text_color(lv.color_make(0xff, 0xff, 0xff), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.scroll_to_view(lv.ANIM.OFF)

    def btn_down_click(self):
        """向下选择"""
        self.clear_state()
        self.cur = self.next_idx(self.cur, len(self.pinmgr_menu_list))
        self.add_state()

    def btn_up_click(self):
        """向上选择"""
        self.clear_state()
        self.cur = self.prev_idx(self.cur, len(self.pinmgr_menu_list))
        self.add_state()

    def btn_menu_click(self):
        """确认选择按键"""
        self.language = EventMesh.publish("persistent_config_get", "language")
        self.pin_enable = EventMesh.publish("persistent_config_get","pin")
        if self.pin_enable == None:
            self.pin_enable = "0"
        if self.cur == 0:
            if self.pin_enable == "0":
                if self.language == "zh":
                    EventMesh.publish("window_show","请先开启PIN")
                else:
                    EventMesh.publish("window_show","Please Open The PIN First")
                EventMesh.publish("about_set_pin_status","0")
                return
        elif self.cur == 1:
            if self.pin_enable == "1":
                if self.language == "zh":
                    EventMesh.publish("window_show","PIN码已开启")
                else:
                    EventMesh.publish("window_show","PIN Code Is Opened")
                EventMesh.publish("about_set_pin_status","1")
                return
        elif self.cur == 2:
            if self.pin_enable == "0":
                if self.language == "zh":
                    EventMesh.publish("window_show","请先开启PIN")
                else:
                    EventMesh.publish("window_show","Please Open The PIN First")
                return
        tem=str(9+self.cur)
        EventMesh.publish("load_screen", {"screen": self.pinmgr_menu_list[self.cur]["screen"],"meta_info":[tem,None]})

    def btn_back_click(self):
        # 返回按键
        EventMesh.publish("load_screen", {"screen": "securitysetting"})

    def btn_pwk_click(self):
        """呼叫挂断返回通话结束页面"""
        EventMesh.publish("load_screen", {"screen": "main"})
        return True

#恢复出厂设置菜单
class ResetScreen(Screen):

    NAME = "reset"

    def __init__(self):
        super().__init__()
        self.cursor_key_id = 0
        self.meta = reset_screen
        self.reset_screen_return = reset_screen_return
        self.reset_screen_sure = reset_screen_sure
        self.reset_label = reset_label
        self.language = "zh"
        # self.call_viwe_title_label = call_viwe_title_label

    def initialization(self):
        self.language = EventMesh.publish("persistent_config_get", "language")
        self.cursor_key_id = int(self.meta_info)
        if self.cursor_key_id == 1:   #测试模式的恢复出厂
            self.reset_screen_return.set_text("")
        else:      #设置界面进入的恢复出厂
            if self.language == "en":
                self.reset_screen_return.set_size(48, 12)
                self.reset_screen_return.set_pos(80, 51)
                self.reset_screen_return.set_text("Cancel")
                self.reset_label.set_text("Factory Reset?")
                self.reset_screen_sure.set_text("Yes")
                self.reset_screen_sure.set_size(24, 12)
            else:
                self.reset_screen_return.set_pos(103, 51)
                self.reset_screen_return.set_size(24, 12)
                self.reset_screen_return.set_text("取消")
                self.reset_label.set_text("恢复出厂设置吗?")
                self.reset_screen_sure.set_text("是")
                self.reset_screen_sure.set_size(12, 12)
            return True
        return True

    def btn_menu_click(self):
        """确认选择按键"""
        self.language = EventMesh.publish("persistent_config_get", "language")
        if self.language == "en":
            EventMesh.publish("window_show","Factory Reseting...")
        else:
            EventMesh.publish("window_show","恢复中...")
        tem = "11"
        if EventMesh.publish("persistent_config_get","eth_status")=="1":
            EventMesh.publish("eth_close",0)
        EventMesh.publish("persistent_config_store",{"volume_ring": tem})
        EventMesh.publish("persistent_config_store",{"key_tone": tem})
        EventMesh.publish("persistent_config_store",{"volume_call": tem})
        EventMesh.publish("persistent_config_store",{"volume_SMS": tem})
        EventMesh.publish("persistent_config_store",{"call_ring": 1})
        EventMesh.publish("persistent_config_store",{"SMS_ring": 3})
        EventMesh.publish("persistent_config_store",{"Alarm_clock_ring": 2})
        EventMesh.publish("set_audio_volume", 0)
        EventMesh.publish("set_audio_volume", 1)
        EventMesh.publish("set_audio_volume", 2)
        EventMesh.publish("set_audio_volume", 3)
        tem = "15"
        EventMesh.publish("persistent_config_store",{"backlight": tem})
        tem = "5"
        EventMesh.publish("persistent_config_store",{"contrast": tem})
        lcd.lcd_contrast(5)
        value = EventMesh.publish("persistent_config_get", "calltransfer")
        if value != None:
            if int(value[0]) != 0:
                voiceCall.setFw(int(value[1]), 0, value[2])
        EventMesh.publish("persistent_config_store",{"calltransfer": ["0","0",""]})
        EventMesh.publish("persistent_config_store",{"netSelect": "0"})
        EventMesh.publish("persistent_config_store",{"quikkey": ["","","",""]})
        EventMesh.publish("persistent_config_store",{"speed_dial_number": ["","","","","","","",""]})
        EventMesh.publish("persistent_config_store",{"blacklist": []})
        EventMesh.publish("persistent_config_store",{"whitelist": []})
        EventMesh.publish("persistent_config_store",{"blocked_numbers": []})
        EventMesh.publish("persistent_config_store",{"whitelistSwitch":"1"})
        EventMesh.publish("persistent_config_store", {"phone_book_all_dict": {"1212": "SLT Call Center",
                                 "1214":"Sisu Connect",
                                 "1247":"Doc call",  
                                 "1225":"eChannelling",
                                 "1265":"SLT Ticketing",
                                 "1296":"Kids Portal",
                                 "1295":"SLT Greetings",
                                 "1299":"SLT Serisara",
                                 "1297":"Feng Shui"}})
        EventMesh.publish("persistent_config_store", {"call_logs_list": []})
        EventMesh.publish("persistent_config_store", {"missed_call": 0})
        EventMesh.publish("persistent_config_store", {"received_call": 0})
        EventMesh.publish("persistent_config_store", {"dialed_call": 0})
        EventMesh.publish("persistent_config_store",{"phrases":["请呼我!","您现在在哪里?","谢谢!","马上到!","今天没空!","","","","","",]})
        EventMesh.publish("persistent_config_store",{"phrases_en":["Please call me!","Where are you now?","Thank you!","Coming soon!","No time today!","","","","","",]})
        EventMesh.publish("persistent_config_store",{"sendInfo":[]})
        EventMesh.publish("persistent_config_store",{"errInfo":[]})
        EventMesh.publish("persistent_config_store",{"messageid":[]})
        EventMesh.publish("persistent_config_store", {"rebootFlag":"1"})
        EventMesh.publish("persistent_config_store", {"mute_state":0})
        EventMesh.publish("persistent_config_store",{"callwait": "0"})
        EventMesh.publish("persistent_config_store",{"disturb":"0"})
        EventMesh.publish("persistent_config_store",{"language": "en"})
        EventMesh.publish("persistent_config_store",{"eth_status": "0"})
        EventMesh.publish("persistent_config_store",{"backlight_switch":1})
        # EventMesh.publish("persistent_config_store", {"phone_index": 1})
        Alarm_Clock_list=[["0","a","00:00","铃声1","自定义",[1,3,5]],["0","b","00:00","铃声1","每天",[8]],["0","c","00:00","铃声2","单次",[]]]
        EventMesh.publish("persistent_config_store", {"Alarm_Clock_list":Alarm_Clock_list})
        wifi_info ={
                "authmode": 5,
                "security": 4,
                "pwd": "12345678",
                "enable": "false",
                "SSID": "KT4_{}".format(EventMesh.publish("get_imei_4"))
            }
        EventMesh.publish("persistent_config_store",{"wifi_info":wifi_info})
        user_info = {
                "username": "admin",
                "password": "admin"
            }
        
        EventMesh.publish("persistent_config_store",{"user_info":user_info})
        eth_status="0"
        EventMesh.publish("persistent_config_store",{"eth_status":eth_status})
        wifi_value_flag=0
        EventMesh.publish("persistent_config_store",{"wifi_value_flag":wifi_value_flag})
        if self.language == "en":
            EventMesh.publish("window_show","Factory Reset Succeed!")
        else:
            EventMesh.publish("window_show","恢复出厂成功")
        utime.sleep(2)
        if EventMesh.publish("persistent_config_get", "rebootFlag") != "1":
            EventMesh.publish("window_show","Rewrite restart")
            utime.sleep(3)
        EventMesh.publish("load_screen", {"screen": "main"})
        ret = None 
        # regCTCC="{}".format(ret)+","+ ""
        # EventMesh.publish("NV_config_set",["regCTCC",regCTCC])
        utime.sleep(2)
        EventMesh.publish("power_close")
        Power.powerRestart()

    def btn_back_click(self):
        # 返回按键
        if self.cursor_key_id == 0:
            EventMesh.publish("load_screen", {"screen": "securitysetting"})
        else:
            return
    def btn_pwk_click(self):
        """呼叫挂断返回通话结束页面"""
        EventMesh.publish("load_screen", {"screen": "main"})
        return True

#开启pin码重启确定
class PinRestartScreen(Screen):

    NAME = "pinrestart"

    def __init__(self):
        super().__init__()
        self.meta = pin_restart_screen
        self.pin_restart_label=pin_restart_label
        self.pin_restart_screen_sure=pin_restart_screen_sure
        self.repin_restart_screen_return=repin_restart_screen_return
        self.language = "zh"

    def initialization(self):
        self.language = EventMesh.publish("persistent_config_get", "language")
        if self.language == "zh":
            self.pin_restart_label.set_text("需重启生效,立即重启?")
            self.pin_restart_screen_sure.set_text("重启")
            self.repin_restart_screen_return.set_pos(100, 51)
            self.repin_restart_screen_return.set_size(24, 12)
            self.repin_restart_screen_return.set_text("取消")
        else:
            self.pin_restart_label.set_pos(5, 10)
            self.pin_restart_label.set_text("If a restart takes effect, restart immediately?")
            self.pin_restart_screen_sure.set_size(48, 12)
            self.pin_restart_screen_sure.set_text("Restart")
            self.repin_restart_screen_return.set_pos(82, 51)
            self.repin_restart_screen_return.set_size(45, 12)
            self.repin_restart_screen_return.set_text("Cancel")
        return True

    def btn_menu_click(self):
        """确认选择按键"""
        EventMesh.publish("persistent_config_store", {"rebootFlag":"1"})
        utime.sleep(2)
        EventMesh.publish("power_close")
        Power.powerRestart()

    def btn_back_click(self):
        # 返回按键
        EventMesh.publish("load_screen", {"screen": "pin"})
        
    def btn_pwk_click(self):
        """呼叫挂断返回通话结束页面"""
        EventMesh.publish("load_screen", {"screen": "main"})
        return True

#安全设置一级菜单
class SecurityScreen(Screen):

    NAME = "securitysetting"
    def __init__(self):
        super().__init__()
        self.meta = security_screen
        self.cur = 0
        self.count = 2
        self.security_setting_list = security_screen_list
        self.security_screen_title = security_screen_title
        self.security_screen_sure = security_screen_sure
        self.security_screen_return = security_screen_return
        self.btn_list = []
        self.language = "zh"
        self.currentButton = None
        self.securitysetting_menu_list = [
            {
                "screen": "reset",
                "title":" 1 恢复出厂",
            },
            {
                "screen": "pin",
                "title":" 2 PIN码管理",
            },
        ]
        self.securitysetting_menu_list_en = [
            {
                "screen": "reset",
                "title":" 1 Reset",
            },
            {
                "screen": "pin",
                "title":" 2 PIN Manage",
            },
        ]

    def initialization(self):
        self.btn_list = []
        self.list_create()
        return True

    def list_create(self):
        self.security_setting_list.delete()
        self.security_setting_list = lv.list(self.meta)
        self.security_setting_list.set_pos(0, 16)
        self.security_setting_list.set_size(128, 32)
        self.security_setting_list.set_style_pad_left(0, 0)
        self.security_setting_list.set_style_pad_top(0, 1)
        self.security_setting_list.set_style_pad_row(0, 0)
        self.security_setting_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
        self.security_setting_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR)
        self.language = EventMesh.publish("persistent_config_get", "language")
        if self.language == "zh":#中文显示
            self.option=self.securitysetting_menu_list
            self.security_screen_title.set_text("安全设置")
            self.security_screen_sure.set_text("选择")
            self.security_screen_return.set_text("返回")
        else:#英文显示
            self.option=self.securitysetting_menu_list_en
            self.security_screen_title.set_text("Security Setting")
            self.security_screen_sure.set_text("Select")
            self.security_screen_return.set_text("Back")
        for idx, item in enumerate(self.option):
            security_obj_list_btn = lv.btn(self.security_setting_list)
            security_obj_list_btn.set_pos(0, 1)
            security_obj_list_btn.set_size(115, 16)
            security_obj_list_btn.add_style(style_list_black, lv.PART.MAIN | lv.STATE.DEFAULT)
            security_obj_list_label = lv.label(security_obj_list_btn)
            security_obj_list_label.center()
            security_obj_list_label.set_pos(0, 2)
            security_obj_list_label.set_size(115, 16)
            security_obj_list_label.set_text(item["title"])
            security_obj_list_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
            self.btn_list.append((security_obj_list_btn, security_obj_list_label))

        self.add_state()

    def add_state(self, cur=None):
        if cur is None:
            cur = self.cur
        self.currentButton = self.security_setting_list.get_child(cur)
        self.currentButton.set_style_bg_color(lv.color_make(0xff, 0xff, 0xff), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.set_style_bg_grad_color(lv.color_make(0xff, 0xff, 0xff), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.btn_list[cur][1].set_style_text_color(lv.color_make(0x00, 0x00, 0x00), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.scroll_to_view(lv.ANIM.OFF)

    def clear_state(self, cur=None):
        if cur is None:
            cur = self.cur
        self.currentButton = self.security_setting_list.get_child(cur)
        self.currentButton.set_style_bg_color(lv.color_make(0x00, 0x00, 0x00), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.set_style_bg_grad_color(lv.color_make(0x00, 0x00, 0x00), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.btn_list[cur][1].set_style_text_color(lv.color_make(0xff, 0xff, 0xff), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.scroll_to_view(lv.ANIM.OFF)

    def btn_down_click(self):
        """向下选择"""
        self.clear_state()
        self.cur = self.next_idx(self.cur, len(self.securitysetting_menu_list))
        self.add_state()

    def btn_up_click(self):
        """向上选择"""
        self.clear_state()
        self.cur = self.prev_idx(self.cur, len(self.securitysetting_menu_list))
        self.add_state()

    def btn_menu_click(self):
        """确认选择按键"""
        self.language = EventMesh.publish("persistent_config_get", "language")
        if self.cur == 0:
            EventMesh.publish("load_screen", {"screen": "reset","meta_info":"0"})
        if self.cur == 1:
            status = sim.getStatus()
            if status == 0:
                if self.language =="zh":
                    EventMesh.publish("window_show", "未插SIM卡")
                else:
                    EventMesh.publish("window_show", "No Insert SIM Card")
                return
            EventMesh.publish("load_screen", {"screen": "pin"})
            # EventMesh.publish("load_screen", {"screen": self.securitysetting_menu_list[self.cur]["screen"]})
    def btn_back_click(self):
        # 返回按键
        EventMesh.publish("load_screen", {"screen": "setting"})

    def btn_pwk_click(self):
        """呼叫挂断返回通话结束页面"""
        EventMesh.publish("load_screen", {"screen": "main"})
        return True

class NetAccessScreen(Screen):
    """入网证信息页面"""
    
    NAME = "net_access"

    def __init__(self):
        super().__init__()
        self.meta = charging_screen
        self.charging_screen_img = charging_screen_img
        self.net_access_code_label = net_access_code_label
        self.net_access_code_label_1 = net_access_code_label_1

    def initialization(self):
        self.charging_screen_img.set_src("U:/static/NetAccess.png")
        self.net_access_code_label_1.set_size(128, 12)
        net_access = EventMesh.publish("NV_config_get", "netAccess")
        if net_access == None:
            net_access = "239105ABCDE0123456789"
        print(net_access)
        self.net_access_code_label.set_pos(0, 50)
        self.net_access_code_label.set_size(32, 12)
        self.net_access_code_label.set_text(net_access[0:6])
        self.net_access_code_label_1.set_pos(32, 50)
        self.net_access_code_label_1.set_size(96, 12)
        self.net_access_code_label_1.set_text(net_access[6:])
        return True

    def btn_back_click(self):
        EventMesh.publish("load_screen", {"screen": "phoneinfo"})

    def btn_pwk_click(self):
        EventMesh.publish("load_screen", {"screen": "main"})
        return True

#话机信息菜单
class PhoneInfoScreen(Screen):
    """话机信息页面"""
    
    NAME = "phoneinfo"

    def __init__(self):
        super().__init__()
        self.cursor_key_id = 0
        self.version = ""
        self.meta = phone_information_screen
        self.phoneinfo_netaccess = phoneinfo_netaccess
        self.phoneinfo_return = phoneinfo_return
        self.language = "zh"

    def initialization(self):
        self.language = EventMesh.publish("persistent_config_get", "language")
        self.cursor_key_id = int(self.meta_info)
        self.version = EventMesh.publish("get_sw_version")
        # print("self.version = ", self.version)
        # if self.version != None:
        #     phoneinfo_sw_label.set_text("SW_VER:{}".format(self.version))
        #     self.version = ""
        if self.cursor_key_id == 0:
            phoneinfo_os_label.set_text("OS:threadx5.0")
            phoneinfo_sw_label.set_text("SW_VER:KT4(1M)_SIP_1.0.1")
            phoneinfo_sw_label.set_long_mode(lv.label.LONG.SCROLL_CIRCULAR) #滚动
            phoneinfo_hw_label.set_text("HW_VER:03KT4(1B)")

            if self.language == "zh":
                phoneinfo_netaccess.set_size(80, 12)
                self.phoneinfo_netaccess.set_text("查看进网许可")
                self.phoneinfo_netaccess.set_long_mode(lv.label.LONG.WRAP)
                self.phoneinfo_return.set_text("返回")
            else:
                phoneinfo_netaccess.set_size(0, 0)
                # self.phoneinfo_netaccess.set_text("Net Access Permit")
                # self.phoneinfo_netaccess.set_long_mode(lv.label.LONG.SCROLL_CIRCULAR)
                self.phoneinfo_return.set_text("Back")
        elif self.cursor_key_id == 1:
            # phoneinfo_netaccess.set_size(0, 0)
            if self.language == "zh":
                self.phoneinfo_return.set_text("返回")
            else:
                self.phoneinfo_return.set_text("Back")
            self.phoneinfo_netaccess.set_text("")
            if self.version != None:
                phoneinfo_sw_label.set_text("SW_VER:{}".format(self.version))
                phoneinfo_sw_label.set_long_mode(lv.label.LONG.SCROLL_CIRCULAR) #滚动
                self.version = ""
        return True

    def btn_back_click(self):
        # 返回按键
        if self.cursor_key_id == 0:   
            EventMesh.publish("load_screen", {"screen": "phoneinfo_select"})
        elif self.cursor_key_id == 1:      
            EventMesh.publish("load_screen", {"screen": "main"})        #暗码进入

    def btn_menu_click(self):
        if self.cursor_key_id == 0:
            if self.language == "zh":
                EventMesh.publish("load_screen", {"screen": "net_access"})
        
    def btn_pwk_click(self):
        """呼叫挂断返回通话结束页面"""
        EventMesh.publish("load_screen", {"screen": "main"})
        return True
#SIP信息菜单
class SIPInfoScreen(Screen):
    """话机信息页面"""
    
    NAME = "sipinfo"

    def __init__(self):
        super().__init__()
        self.meta = sip_information_screen
        self.sipinfo_title = sipinfo_title
        self.sip_information_list = sip_information_list
        self.sipinfo_return = sip_information_return
        self.cur = 0
        self.count = 6
        self.flag  = 0
        self.option = None
        self.currentButton = None
        self.ipv4addr = ""
        self.btn_list = []
        self.sip_info_win=[
            "服务器地址:",
            "端口:",
            "账号:",
            "域:",
            "显示名:",
            "SIM地址:",
        ]
        self.sip_info_win_en=[
            "server",
            "port",
            "user",
            "domain",
            "displayName",
            "simip",
        ]
        self.sip_info = {
            "server": "",
            "port": "",
            "user": "",
            "domain": "",
            "displayName": "",
            "simip": "",
        }

    def initialization(self):
        if not self.ipv4addr:  # 更简洁的空值检查
            try:
                print("获取IPv4地址...")
                result = dataCall.getInfo(1, 0)
                # 确保结果包含IPv4地址
                if result and len(result) > 2 and len(result[2]) > 2:
                    self.ipv4addr = result[2][2]
                    print("获取到IPv4地址:",self.ipv4addr)
                else:
                    print("错误: 无法获取有效的IPv4地址")
                    self.ipv4addr = ""
            except Exception as e:
                print("获取IP地址时出错: {e}")
                self.ipv4addr = ""
        temp_info = EventMesh.publish("persistent_config_get", "sip_info")
        if temp_info != None:
            self.sip_info = {
                    "server": temp_info[2],
                    "port": temp_info[3],
                    "user": temp_info[0],
                    "password": temp_info[1],
                    "domain": temp_info[4],
                    "displayName": temp_info[5],
                    "simip": self.ipv4addr,
                }
        else:
            self.sip_info = {
                "server": "",
                "port": "",
                "user": "",
                "domain": "",
                "displayName": "",
                "simip": self.ipv4addr,
            }
        self.btn_list = []
        self.list_create()
        return True

    def list_create(self):
        self.sip_information_list.delete()
        self.sip_information_list = lv.list(self.meta)
        self.sip_information_list.set_pos(0, 16)
        self.sip_information_list.set_size(128, 32)
        self.sip_information_list.set_style_pad_left(0, 0)
        self.sip_information_list.set_style_pad_top(0, 1)
        self.sip_information_list.set_style_pad_row(0, 0)
        self.sip_information_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
        self.sip_information_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR)
        self.sip_information_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.SCROLLED)
        self.language = EventMesh.publish("persistent_config_get", "language")
        if self.language == "zh":#中文显示
            self.option=self.sip_info_win
            self.sipinfo_title.set_text("IMS信息")
            self.sipinfo_return.set_text("返回")
            self.sipinfo_return.set_pos(80, 51)
        else:#英文显示
            self.option=self.sip_info_win_en
            self.sipinfo_title.set_text("SIP information")
            self.sipinfo_return.set_size(48, 12)
            self.sipinfo_return.set_text("Back")
        for idx, item in enumerate(self.option):
            
            sip_information_list_btn = lv.btn(self.sip_information_list)
            sip_information_list_btn.set_pos(0, 1)
            sip_information_list_btn.set_size(115, 16)
            sip_information_list_btn.add_style(style_list_black, lv.PART.MAIN | lv.STATE.DEFAULT)
            sip_information_list_title_label = lv.label(sip_information_list_btn)
            sip_information_list_title_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
            sip_information_list_title_label.set_pos(1, 2)
            sip_information_list_title_label.set_size(66, 16)
            sip_information_list_title_label.set_text(item)
            sip_information_list_content_label = lv.label(sip_information_list_btn)
            if idx == 4:
                sip_information_list_content_label.set_pos(67, 2)
                sip_information_list_content_label.set_size(60, 16)
            else:
                sip_information_list_content_label.set_pos(42, 2)
                sip_information_list_content_label.set_size(75, 16)
            sip_information_list_content_label.set_long_mode(lv.label.LONG.SCROLL_CIRCULAR)
            sip_information_list_content_label.set_style_anim_speed(3,0)
            temp_str = '{}'.format(self.sip_info_win_en[idx])
            print("temp_str:",temp_str)
            sip_information_list_content_label.set_text(self.sip_info[temp_str] if self.sip_info[temp_str]!=None else "")
            self.btn_list.append((sip_information_list_btn, sip_information_list_title_label,sip_information_list_content_label))
        self.add_state()

    def add_state(self, cur=None):
        if cur is None:
            cur = self.cur
        self.currentButton = self.sip_information_list.get_child(cur)
        self.currentButton.set_style_bg_color(lv.color_make(0xff, 0xff, 0xff), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.set_style_bg_grad_color(lv.color_make(0xff, 0xff, 0xff), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.btn_list[cur][1].set_style_text_color(lv.color_make(0x00, 0x00, 0x00), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.btn_list[cur][2].set_style_text_color(lv.color_make(0x00, 0x00, 0x00), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.scroll_to_view(lv.ANIM.OFF)

    def clear_state(self, cur=None):
        if cur is None:
            cur = self.cur
        self.currentButton = self.sip_information_list.get_child(cur)
        self.currentButton.set_style_bg_color(lv.color_make(0x00, 0x00, 0x00), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.set_style_bg_grad_color(lv.color_make(0x00, 0x00, 0x00), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.btn_list[cur][1].set_style_text_color(lv.color_make(0xff, 0xff, 0xff), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.btn_list[cur][2].set_style_text_color(lv.color_make(0xff, 0xff, 0xff), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.scroll_to_view(lv.ANIM.OFF)

    def btn_up_click(self):
        self.clear_state()
        self.cur = self.prev_idx(self.cur, self.count)
        self.add_state()

    def btn_down_click(self):
        self.clear_state()
        self.cur = self.next_idx(self.cur, self.count)
        self.add_state()

    def btn_back_click(self):
        # 返回按键
        if self.cur > 0:
            self.clear_state()
            self.cur = 0
        EventMesh.publish("load_screen", {"screen": "setting"})
        return True

    def btn_pwk_click(self):
        """呼叫挂断返回通话结束页面"""
        if self.cur > 0:
            self.clear_state()
            self.cur = 0
        EventMesh.publish("load_screen", {"screen": "main"})
        return True
class KeypadInput(Screen):
    """
    快捷号码设置页面
    """
    NAME = "keypad_input"
    def __init__(self):
        super().__init__()
        self.meta = call_screen
        self.cursor_state = 0
        self.cursor_task_id = 0
        self.cursor_key_id = 0
        self.hostpot_ssid_num = ""
        self.hostpot_password_num = ""
        self.num_len = 0
        self.key = []
        self.pin_puk_count = [3,10]
        self.style_18_or_20 = 0
        self.pin_current_step = 0
        self.puk_verify_number = ""
        self.pin_verify_number = ""
        self.pin_current_number = ""
        self.input_phone_number = ""
        self.call_phone_flag = False
        self.call_viwe_options_label = call_viwe_options_label
        self.call_viwe_screen = call_viwe_screen
        self.call_viwe_info_label_18 = call_viwe_info_label_18 
        self.call_viwe_info_label_20 = call_viwe_info_label_20    
        self.call_viwe_title_label = call_viwe_title_label
        self.call_viwe_cursor_label = call_viwe_cursor_label
        self.call_viwe_back_label = call_viwe_back_label
        self.language = "zh"
        self.tone_timeout_timer_runing = 0
        self.tone_timeout_timer = osTimer()

    def initialization(self):
        # 开启游标闪烁
        self.start_cursor_flicker()
        self.key = []
        self.style_18_or_20 = 0
        self.pin_current_step = 0
        self.puk_verify_number = ""
        self.pin_verify_number = ""
        self.pin_current_number = ""

        # 支持字符串类型的cursor_key_id（用于禁拨号码管理）
        if isinstance(self.meta_info[0], str):
            self.cursor_key_id = self.meta_info[0]
        else:
            self.cursor_key_id = int(self.meta_info[0])

        # 添加输入模式状态控制（用于禁拨号码管理）
        self.input_mode_active = False
        # 对于禁拨号码添加/删除界面，默认输入模式为非激活状态
        if self.cursor_key_id == "blocked_add" or self.cursor_key_id == "blocked_delete":
            self.input_mode_active = False
        else:
            # 其他界面默认激活输入模式（保持原有行为）
            self.input_mode_active = True

        if len(self.meta_info)==3:
            self.Alarm_Clock_num=self.meta_info[2]
        self.display_create()
        if self.meta_info[1]:
            self.input_phone_number = self.meta_info[1]
        else:
            self.input_phone_number = ""
        self.update_phone_number(None, self.input_phone_number)
        if self.cursor_key_id == 8:
            self.tone_timeout_timer_runing = 1
            self.tone_timeout_timer.start(60000, 0, self.stop_tone_to_howler)
        return True

    def post_processor_after_instantiation(self):
        EventMesh.subscribe("set_font", self.set_font)
        EventMesh.subscribe("get_cur_number_len", self.get_cur_number_len)
        EventMesh.subscribe("reset_input_number", self.reset_input_number)
        EventMesh.subscribe("get_hostpot_ssid_num", self.get_hostpot_ssid_num)

    def display_create(self):
        self.language = EventMesh.publish("persistent_config_get", "language")
        # 检查cursor_key_id是否为整数类型，避免字符串比较错误
        if isinstance(self.cursor_key_id, int) and self.cursor_key_id >= 9 and self.cursor_key_id < 14:
            if self.language == "zh":
                self.call_viwe_title_label.set_pos(0, 0)
                self.call_viwe_title_label.set_size(128,12)
                self.call_viwe_options_label.set_text("确认")
                if self.cursor_key_id == 9:
                    self.call_viwe_options_label.set_text("保存")
                    self.call_viwe_title_label.set_text("请输入当前PIN码")
                elif self.cursor_key_id == 10:
                    self.call_viwe_title_label.set_text("请输入当前PIN码")
                elif self.cursor_key_id == 11:
                    self.call_viwe_title_label.set_text("请输入当前PIN码")
                elif self.cursor_key_id == 12:
                    self.pin_puk_count = sim.getPinRemAttempts()
                    pin_cnt = self.pin_puk_count[0]
                    self.call_viwe_title_label.set_text("请输入PIN码 剩余"+str(pin_cnt)+"次")
                elif self.cursor_key_id == 13:
                    self.pin_puk_count = sim.getPinRemAttempts()
                    puk_cnt = self.pin_puk_count[1]
                    self.call_viwe_title_label.set_text("请输入PUK码 剩余"+str(puk_cnt)+"次")
                self.call_viwe_title_label.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
            else:
                self.call_viwe_title_label.set_pos(0, 0)
                self.call_viwe_title_label.set_size(128,12)
                self.call_viwe_options_label.set_text("OK")
                if self.cursor_key_id == 9:
                    self.call_viwe_options_label.set_text("Save")
                    self.call_viwe_title_label.set_text("Please Enter The Current PIN")
                elif self.cursor_key_id == 10:
                    self.call_viwe_title_label.set_text("Please Enter The Current PIN")
                elif self.cursor_key_id == 11:
                    self.call_viwe_title_label.set_text("Please Enter The Current PIN")
                elif self.cursor_key_id == 12:
                    self.pin_puk_count = sim.getPinRemAttempts()
                    pin_cnt = self.pin_puk_count[0]
                    self.call_viwe_title_label.set_text("Please Enter The PIN Remainder"+str(pin_cnt)+"times")
                elif self.cursor_key_id == 13:
                    self.pin_puk_count = sim.getPinRemAttempts()
                    puk_cnt = self.pin_puk_count[1]
                    self.call_viwe_title_label.set_text("Please Enter The PIN Remainder"+str(puk_cnt)+"times")
                self.call_viwe_title_label.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
            self.call_viwe_screen.set_pos(0, 16)
            self.call_viwe_info_label_18.set_pos(0, 16)
            self.call_viwe_info_label_18.set_size(128, 24)
            self.call_viwe_info_label_20.set_pos(0, 16)
            self.call_viwe_info_label_20.set_size(128, 24)

        elif isinstance(self.cursor_key_id, int) and (self.cursor_key_id < 9 or self.cursor_key_id >= 14) or isinstance(self.cursor_key_id, str):
            if self.language == "zh":
                self.call_viwe_options_label.set_text("保存")
                self.call_viwe_title_label.set_text("")
            else:
                self.call_viwe_options_label.set_text("Save")
                self.call_viwe_title_label.set_text("")
            self.call_viwe_title_label.set_pos(51, 51)
            self.call_viwe_title_label.set_size(12,12)
            self.call_viwe_screen.set_pos(0, 4)
            self.call_viwe_info_label_18.set_pos(0, 4)
            self.call_viwe_info_label_18.set_size(128, 48)
            self.call_viwe_info_label_20.set_pos(0, 4)
            self.call_viwe_info_label_20.set_size(128, 48)

            # 禁拨号码管理界面显示
            if self.cursor_key_id == "blocked_add":
                self.call_viwe_title_label.set_pos(0, 0)
                self.call_viwe_title_label.set_size(128,10)
                if self.language == "zh":
                    self.call_viwe_title_label.set_text("添加禁拨号码")
                    self.call_viwe_options_label.set_text("添加")
                else:
                    self.call_viwe_title_label.set_text("Add Blocked Number")
                    self.call_viwe_options_label.set_text("Add")
                self.call_viwe_title_label.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)

                # 显示当前禁拨号码列表
                self.display_blocked_numbers_list()

                # 输入框位置调整到列表下方
                self.call_viwe_screen.set_pos(0, 40)
                self.call_viwe_info_label_18.set_pos(0, 40)
                self.call_viwe_info_label_18.set_size(128, 12)
                self.call_viwe_info_label_18.set_long_mode(lv.label.LONG.DOT)
                self.call_viwe_info_label_20.set_pos(0, 40)
                self.call_viwe_info_label_20.set_size(128, 12)
                self.call_viwe_info_label_20.set_long_mode(lv.label.LONG.DOT)
            elif self.cursor_key_id == "blocked_delete":
                self.call_viwe_title_label.set_pos(0, 0)
                self.call_viwe_title_label.set_size(128,10)
                if self.language == "zh":
                    self.call_viwe_title_label.set_text("删除禁拨号码")
                    self.call_viwe_options_label.set_text("删除")
                else:
                    self.call_viwe_title_label.set_text("Delete Blocked Number")
                    self.call_viwe_options_label.set_text("Delete")
                self.call_viwe_title_label.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)

                # 显示当前禁拨号码列表
                self.display_blocked_numbers_list()

                # 输入框位置调整到列表下方
                self.call_viwe_screen.set_pos(0, 40)
                self.call_viwe_info_label_18.set_pos(0, 40)
                self.call_viwe_info_label_18.set_size(128, 12)
                self.call_viwe_info_label_18.set_long_mode(lv.label.LONG.DOT)
                self.call_viwe_info_label_20.set_pos(0, 40)
                self.call_viwe_info_label_20.set_size(128, 12)
                self.call_viwe_info_label_20.set_long_mode(lv.label.LONG.DOT)
            elif self.cursor_key_id==47:
                self.call_viwe_title_label.set_pos(0, 0)
                self.call_viwe_title_label.set_size(128,12)
                self.call_viwe_title_label.set_text("请输入4位有效时间:" if self.language=="zh" else "Enter time of 4 digits:")
                self.call_viwe_title_label.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
                self.call_viwe_screen.set_pos(0, 16)
                self.call_viwe_info_label_18.set_pos(0, 16)
                self.call_viwe_info_label_18.set_size(128, 24)
                self.call_viwe_info_label_20.set_pos(0, 16)
                self.call_viwe_info_label_20.set_size(128, 24)

    def get_input_method(self):
        return "123"

    def cursor_flicker_task(self):
        while True:
            if self.cursor_state:
                self.call_viwe_cursor_label.set_style_bg_color(lv.color_make(0xff, 0xff, 0xff), 0)
                self.cursor_state = 0
            else:
                self.call_viwe_cursor_label.set_style_bg_color(lv.color_make(0x00, 0x00, 0x00), 0)
                self.cursor_state = 1
            utime.sleep_ms(600)

    def stop_cursor_flicker(self):
        if self.cursor_task_id:
            try:
                _thread.stop_thread(self.cursor_task_id)
            except:
                print("stop_cursor_flicker error")
            finally:
                self.cursor_task_id = 0

    def start_cursor_flicker(self):
        if not self.cursor_task_id:
            self.cursor_task_id = _thread.start_new_thread(self.cursor_flicker_task, ())

    def display_blocked_numbers_list(self):
        """显示当前禁拨号码列表"""
        # 获取当前禁拨号码列表
        blocked_numbers = EventMesh.publish("persistent_config_get", "blocked_numbers")
        if blocked_numbers is None:
            blocked_numbers = []

        # 创建显示文本
        if len(blocked_numbers) == 0:
            if self.language == "zh":
                list_text = "当前无禁拨号码"
            else:
                list_text = "No blocked numbers"
        else:
            # 限制显示数量以避免界面过长
            display_count = min(len(blocked_numbers), 3)
            list_text = ""
            for i in range(display_count):
                if i > 0:
                    list_text += " "
                list_text += blocked_numbers[i]

            # 如果还有更多号码，显示省略号
            if len(blocked_numbers) > display_count:
                list_text += "..."

        # 创建或更新显示标签
        if not hasattr(self, 'blocked_list_label'):
            self.blocked_list_label = lv.label(self.meta)
            self.blocked_list_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)

        self.blocked_list_label.set_pos(0, 12)
        self.blocked_list_label.set_size(128, 26)
        self.blocked_list_label.set_text(list_text)
        self.blocked_list_label.set_long_mode(lv.label.LONG.SCROLL_CIRCULAR)
        self.blocked_list_label.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)

    def update_phone_number(self, topic, phone_num):
        """输入号码"""
        self.language = EventMesh.publish("persistent_config_get", "language")
        phone_num_len = len(phone_num)
        if isinstance(self.cursor_key_id, int) and self.cursor_key_id >= 9 and self.cursor_key_id < 14:#pin输入相关
            print("121212")
            self.set_style_20()
            self.set_style=20
            pos_x = phone_num_len * 14
            self.call_viwe_info_label_20.set_pos(0, 0)
            self.call_viwe_cursor_label.set_pos(pos_x, 22)
        elif isinstance(self.cursor_key_id, int) and (self.cursor_key_id < 9 or self.cursor_key_id >= 14) or isinstance(self.cursor_key_id, str):
            self.set_style_20()
            self.set_style=20
            if phone_num_len <= 8:
                pos_x = phone_num_len * 14
                self.call_viwe_info_label_20.set_pos(0, 4)
                self.call_viwe_cursor_label.set_pos(pos_x, 29)
            else:
                self.set_style_18()
                self.set_style=18
                self.call_viwe_info_label_18.set_pos(0, 0)
                if phone_num_len <= 10:
                    pos_x = phone_num_len * 12
                    self.call_viwe_cursor_label.set_pos(pos_x, 22)
                else:
                    pos_x = (phone_num_len-10) * 12
                    self.call_viwe_cursor_label.set_pos(pos_x, 44)
        self.call_viwe_info_label_18.set_text(self.input_phone_number)
        self.call_viwe_info_label_20.set_text(self.input_phone_number)      
        if len(self.input_phone_number)==0:
            if self.language == "zh":
                self.call_viwe_back_label.set_pos(104, 51)
                self.call_viwe_back_label.set_text("返回")
            else:
                self.call_viwe_back_label.set_pos(104, 51)
                self.call_viwe_back_label.set_text("Back")
            # self.num_len = 0
            # EventMesh.publish("persistent_config_store",{"num_len":self.num_len})
        else :
            if self.language == "zh":
                self.call_viwe_back_label.set_pos(104, 51)
                self.call_viwe_back_label.set_text("删除")
            else:
                self.call_viwe_back_label.set_pos(90, 51)
                self.call_viwe_back_label.set_size(48, 12)
                self.call_viwe_back_label.set_text("Delete")
        if self.cursor_key_id == 8:
            if self.language == "zh":
                self.call_viwe_options_label.set_text("选项")
            else:
                self.call_viwe_options_label.set_size(48,12)
                self.call_viwe_options_label.set_text("Select")

    def backspace_phone_number(self, topic=None, data=None):
        self.language = EventMesh.publish("persistent_config_get", "language")
        phone_number_len = len(self.input_phone_number)
        if phone_number_len == 0:
            # 返回待机页面
            # 处理字符串类型的cursor_key_id（禁拨号码管理）
            if self.cursor_key_id == "blocked_add" or self.cursor_key_id == "blocked_delete":
                # 重置输入模式状态
                self.input_mode_active = False
                EventMesh.publish("load_screen", {"screen": "blocked_numbers_main"})
            elif isinstance(self.cursor_key_id, int) and self.cursor_key_id < 4:
                EventMesh.publish("load_screen", {"screen": "quickkey"})
            elif isinstance(self.cursor_key_id, int) and self.cursor_key_id < 8:
                EventMesh.publish("load_screen", {"screen": "calltransfer"})
            elif self.cursor_key_id == 8:
                EventMesh.publish("set_handfree_state", 0)
                EventMesh.publish("load_screen", {"screen": "main"})
            elif isinstance(self.cursor_key_id, int) and self.cursor_key_id < 12:
                EventMesh.publish("load_screen", {"screen": "pin"})
            elif self.cursor_key_id == 12 :
                if self.language == "zh":
                    EventMesh.publish("window_show","请输入PIN码")
                else:
                    EventMesh.publish("window_show","Please Enter PIN")
            elif self.cursor_key_id == 13:
                if self.language == "zh":
                    EventMesh.publish("window_show","请输入号码")
                else:
                    EventMesh.publish("window_show","Please Enter number") 
            elif isinstance(self.cursor_key_id, int) and self.cursor_key_id >= 14 and self.cursor_key_id < 22 :
                EventMesh.publish("load_screen", {"screen": "speed_dial"})
            elif self.cursor_key_id == 22:
                EventMesh.publish("load_screen", {"screen": "black_white","meta_info":"1"})
            elif self.cursor_key_id == 23:
                EventMesh.publish("load_screen", {"screen": "black_white","meta_info":"2"})
            # elif self.cursor_key_id == 24 or self.cursor_key_id == 25:#返回SOS号码设置界面
            #     EventMesh.publish("load_screen", {"screen": "SOS_setting"})    
            # elif self.cursor_key_id > 25 and self.cursor_key_id < 46:
            #     EventMesh.publish("load_screen", {"screen": "sos_white_option"})
            elif self.cursor_key_id == 47:#闹钟时间编辑界面  47
                meta_info={"Alarm_Clock_num":self.Alarm_Clock_num}
                EventMesh.publish("load_screen", {"screen": "Add_alarm_clock","meta_info":meta_info})
            elif self.cursor_key_id == 48 or self.cursor_key_id == 49:#热点设置界面  48 49
                EventMesh.publish("load_screen", {"screen": "hotspot_edit"})
            return
        else:
            if self.cursor_key_id == 47:#闹钟时间编辑界面
                if phone_number_len == 3:
                    self.input_phone_number = self.input_phone_number[:-2]
                elif phone_number_len == 1:
                    self.input_phone_number = ""
                else:
                    self.input_phone_number = self.input_phone_number[:-1]
            else:
                if phone_number_len == 1:
                    self.input_phone_number = ""
                    # 如果是禁拨号码界面且输入框清空，重置输入模式状态
                    if (self.cursor_key_id == "blocked_add" or self.cursor_key_id == "blocked_delete"):
                        self.input_mode_active = False
                else:
                    self.input_phone_number = self.input_phone_number[:-1]
        self.update_phone_number(None, self.input_phone_number)

    def set_style_18(self):
        if self.style_18_or_20 == 1:
            self.style_18_or_20 = 0
            # self.call_viwe_info_label.remove_style(style_font_black_montserrat_20,lv.PART.MAIN| lv.STATE.DEFAULT)
            # self.call_viwe_info_label.add_style(style_font_black_montserrat_18, lv.PART.MAIN | lv.STATE.DEFAULT)
            self.call_viwe_info_label_18.clear_flag(1)
            self.call_viwe_info_label_20.add_flag(1)
    def set_style_20(self):
        if self.style_18_or_20 == 0:
            self.style_18_or_20 = 1
            # self.call_viwe_info_label.remove_style(style_font_black_montserrat_18,lv.PART.MAIN | lv.STATE.DEFAULT)
            # self.call_viwe_info_label.add_style(style_font_black_montserrat_20, lv.PART.MAIN | lv.STATE.DEFAULT)
            self.call_viwe_info_label_18.add_flag(1)
            self.call_viwe_info_label_20.clear_flag(1)
            
    def set_font(self, event, data):
        self.set_style_20()

    def btn_menu_click(self):
        self.language = EventMesh.publish("persistent_config_get", "language")

        # 处理禁拨号码
        if self.cursor_key_id == "blocked_add":
            # 如果输入模式未激活，点击"Add"按钮激活输入模式
            if not self.input_mode_active:
                self.input_mode_active = True
                if self.language == "zh":
                    EventMesh.publish("window_show", "输入模式已激活，请输入号码")
                else:
                    EventMesh.publish("window_show", "Input mode activated, enter number")
                return

            # 输入模式已激活，执行添加操作
            if self.input_phone_number == "":
                EventMesh.publish("window_show","请输入号码" if self.language == "zh" else "Please enter number")
            else:
                blocked_numbers = EventMesh.publish("persistent_config_get", "blocked_numbers")
                if blocked_numbers is None:
                    blocked_numbers = []
                if self.input_phone_number not in blocked_numbers:
                    blocked_numbers.append(self.input_phone_number)
                    EventMesh.publish("persistent_config_store", {"blocked_numbers": blocked_numbers})
                    EventMesh.publish("window_show","添加成功" if self.language == "zh" else "Added successfully")
                else:
                    EventMesh.publish("window_show","号码已存在" if self.language == "zh" else "Number exists")
                # 无论添加成功还是失败，都返回主界面
                EventMesh.publish("load_screen", {"screen": "blocked_numbers_main"})
            return
        elif self.cursor_key_id == "blocked_delete":
            # 如果输入模式未激活，点击"Delete"按钮激活输入模式
            if not self.input_mode_active:
                self.input_mode_active = True
                if self.language == "zh":
                    EventMesh.publish("window_show", "输入模式已激活，请输入号码")
                else:
                    EventMesh.publish("window_show", "Input mode activated, enter number")
                return

            # 输入模式已激活，执行删除操作
            if self.input_phone_number == "":
                EventMesh.publish("window_show","请输入号码" if self.language == "zh" else "Please enter number")
            else:
                blocked_numbers = EventMesh.publish("persistent_config_get", "blocked_numbers")
                if blocked_numbers and self.input_phone_number in blocked_numbers:
                    blocked_numbers.remove(self.input_phone_number)
                    EventMesh.publish("persistent_config_store", {"blocked_numbers": blocked_numbers})
                    EventMesh.publish("window_show","删除成功" if self.language == "zh" else "Deleted successfully")
                else:
                    EventMesh.publish("window_show","号码不存在" if self.language == "zh" else "Number not found")
                EventMesh.publish("load_screen", {"screen": "blocked_numbers_main"})
            return

        if isinstance(self.cursor_key_id, int) and self.cursor_key_id < 4:      #cursor_key_id 0-3是4个快捷键
            self.key = EventMesh.publish("persistent_config_get", "quikkey")
            if self.key == None:
                self.key = ["","","",""]
            self.key[self.cursor_key_id]=self.input_phone_number
            EventMesh.publish("persistent_config_store",{"quikkey": [self.key[0],self.key[1],self.key[2],self.key[3]]})
            EventMesh.publish("load_screen", {"screen": "quickkey"})
            if self.language == "zh":
                EventMesh.publish("window_show","快捷键保存成功")
            else:
                EventMesh.publish("window_show","Save Successfully")
        elif isinstance(self.cursor_key_id, int) and self.cursor_key_id < 8:    #cursor_key_id 4-7是呼叫转移
            self.input_transfer_setting()

        elif self.cursor_key_id == 8:   #cursor_key_id 8是呼叫拨号
            meta_info = {"number": self.input_phone_number , "name": ""}
            EventMesh.publish("load_screen", {"screen": "dialoption", "meta_info": meta_info})

        elif isinstance(self.cursor_key_id, int) and self.cursor_key_id < 12:   #cursor_key_id 9-11是PIN码三个界面
            print("PIN码管理")
            phone_number_len = len(self.input_phone_number)
            if phone_number_len < 4 or phone_number_len > 8:
                if self.language == "zh":
                    EventMesh.publish("window_show","请输入4-8位PIN码")
                else:
                    EventMesh.publish("window_show","Please Enter 4-8-Digit PIN")
            else:
                self.input_pin_setting()
            self.set_style_20()
        elif isinstance(self.cursor_key_id, int) and self.cursor_key_id < 13:   #cursor_key_id 12是PIN码验证界面
            
            phone_number_len = len(self.input_phone_number)
            if phone_number_len < 4 or phone_number_len > 8:
                if self.language == "zh":
                    EventMesh.publish("window_show","请输入4-8位PIN码")
                else:
                    EventMesh.publish("window_show","Please Enter 4-8-Digit PIN")
            else:
                if sim.verifyPin(self.input_phone_number) == 0:
                    EventMesh.publish("persistent_config_store",{"PinCode": self.input_phone_number})
                    EventMesh.publish("restart_simcard_status_timer")
                    EventMesh.publish("load_screen", {"screen": "main"})
                    self.set_style_20()
                    return
                else:
                    EventMesh.publish("window_show","PIN code error")
                    self.pin_puk_count = sim.getPinRemAttempts()
                    pin_cnt = self.pin_puk_count[0]
                    print("pin_cnt=", pin_cnt)
                    if self.language == "zh":
                        self.call_viwe_title_label.set_text("请输入PIN码 剩余"+str(pin_cnt)+"次")
                    else:
                        self.call_viwe_title_label.set_text("Please Enter PIN Remainder"+str(pin_cnt)+"Times")
                    if pin_cnt == 0:
                        self.cursor_key_id = 13
                        self.style_18_or_20 = 1
                        self.pin_current_step = 0
                        self.puk_verify_number = ""
                        self.pin_verify_number = ""
                        self.pin_current_number = ""
                        self.display_create()
            self.input_phone_number = ""
            self.update_phone_number(None, self.input_phone_number)
        elif isinstance(self.cursor_key_id, int) and self.cursor_key_id < 14:   #cursor_key_id 13是PUK码验证界面
            phone_number_len = len(self.input_phone_number)
            print("phone_number_len——13=", phone_number_len)
            if phone_number_len < 4 or phone_number_len > 8:
                if len(self.puk_verify_number) == 0:
                    if self.language == "zh":
                        EventMesh.publish("window_show","请正确输入PUK码")
                    else:
                        EventMesh.publish("window_show","Please Enter The PUKCodeCorrectly")
                else:
                    if self.language == "zh":
                        EventMesh.publish("window_show","请输入4位PIN码")
                    else:
                        EventMesh.publish("window_show","Please Enter 4-Digit PIN")
            else:
                if len(self.puk_verify_number) == 0:
                    self.puk_verify_number = self.input_phone_number
                    if self.language == "zh":
                        self.call_viwe_title_label.set_text("请输入新PIN码")
                    else:
                        self.call_viwe_title_label.set_text("Please Enter A New PIN")
                        
                else:
                    if sim.unblockPin(self.puk_verify_number,self.input_phone_number) == 0:
                        EventMesh.publish("persistent_config_store",{"PinCode": self.input_phone_number})
                        EventMesh.publish("restart_simcard_status_timer")
                        EventMesh.publish("load_screen", {"screen": "main"})
                        self.set_style_20()
                    else:
                        self.puk_verify_number = ""
                        self.pin_puk_count = sim.getPinRemAttempts()
                        puk_cnt = self.pin_puk_count[1]
                        if self.language == "zh":
                            self.call_viwe_title_label.set_text("请输入PUK码 剩余"+str(puk_cnt)+"次")
                            EventMesh.publish("window_show","校验PUK码失败")
                        else:
                            self.call_viwe_title_label.set_text("Please Enter PUK Remainder"+str(puk_cnt)+"Times")
                            EventMesh.publish("window_show","Failed To Verify PUK Code")
            self.input_phone_number = ""
            self.update_phone_number(None, self.input_phone_number)
        elif isinstance(self.cursor_key_id, int) and self.cursor_key_id < 22:   #cursor_key_id 14-21是快捷拨号界面
            key = EventMesh.publish("persistent_config_get", "speed_dial_number")
            if key == None:
                key = ["","","","","","","",""]
            if self.input_phone_number == "":
                if self.language == "zh":
                    EventMesh.publish("window_show","请输入号码")
                else:
                    EventMesh.publish("window_show","Please enter the number")
            else:    
                key[self.cursor_key_id-14]=self.input_phone_number
                EventMesh.publish("persistent_config_store",{"speed_dial_number": [key[0],key[1],key[2],key[3],\
                    key[4],key[5],key[6],key[7]]})
                print("快捷号码设置成功")
                EventMesh.publish("load_screen", {"screen": "speed_dial"})
        # 禁拨号码功能已移至btn_left_click方法处理

        elif isinstance(self.cursor_key_id, int) and self.cursor_key_id < 24:   #cursor_key_id 22-23是黑/白名单界面
            if self.meta_info[2] != None:
                index = self.meta_info[2]
            whitelist = EventMesh.publish("persistent_config_get", "whitelist")
            blacklist = EventMesh.publish("persistent_config_get", "blacklist")
            if whitelist == None:
                whitelist = []
            if blacklist == None:
                blacklist = []
            if self.cursor_key_id == 22:
                if self.input_phone_number == "":
                    if self.language == "zh":
                        EventMesh.publish("window_show","请输入号码")
                    else:
                        EventMesh.publish("window_show","Please enter the number")
                else:
                    if self.input_phone_number in blacklist:
                        if self.language == "zh":
                            EventMesh.publish("window_show","名单中已存在")
                        else:
                            EventMesh.publish("window_show","Already exists in the list")
                        return
                    if self.input_phone_number in whitelist:
                        if self.language == "zh":
                            EventMesh.publish("window_show","白名单中已存在")
                        else:
                            EventMesh.publish("window_show","Already exists in the whitelist")
                        return
                    if index >= 0:
                        blacklist[index] = self.input_phone_number
                    else:
                        blacklist.append(self.input_phone_number)
                    EventMesh.publish("persistent_config_store",{"blacklist":blacklist})
                    EventMesh.publish("load_screen", {"screen": "black_white","meta_info":"1"})
            elif self.cursor_key_id == 23:
                if self.input_phone_number == "":
                    if self.language == "zh":
                        EventMesh.publish("window_show","请输入号码")
                    else:
                        EventMesh.publish("window_show","Please enter the number")
                else:
                    if self.input_phone_number in whitelist:
                        if self.language == "zh":
                            EventMesh.publish("window_show","名单中已存在")
                        else:
                            EventMesh.publish("window_show","Already exists in the list")
                        return
                    if self.input_phone_number in blacklist:
                        if self.language == "zh":
                            EventMesh.publish("window_show","黑名单中已存在")
                        else:
                            EventMesh.publish("window_show","Already exists in the blacklist")
                        return
                    if index >= 0:
                        whitelist[index] = self.input_phone_number
                    else:
                        whitelist.append(self.input_phone_number)
                    EventMesh.publish("persistent_config_store",{"whitelist":whitelist})
                    EventMesh.publish("load_screen", {"screen": "black_white","meta_info":"2"})

        # elif self.cursor_key_id < 26: #SOS号码输入界面
        #     print("返回SOS设置界面")
        #     if self.input_phone_number == "":
        #         if self.language == "zh":
        #             EventMesh.publish("window_show","请输入号码")
        #         else:
        #             EventMesh.publish("window_show","Please enter the number")
        #         input_number=""
        #     else:    
        #         input_number=self.input_phone_number
        #     if self.cursor_key_id == 24:
        #         if input_number=="":
        #             EventMesh.publish("window_show","请输入1-20位SOS号码")
        #             return
        #         EventMesh.publish("persistent_config_store",{"sos_number":input_number})
        #     elif self.cursor_key_id == 25:
        #         if input_number=="":
        #             EventMesh.publish("window_show","请输入0-999秒触发时间")
        #             return
        #         EventMesh.publish("persistent_config_store",{"trigger_time":input_number})
        #     EventMesh.publish("load_screen", {"screen": "SOS_setting"})
            
        # elif self.cursor_key_id > 25 and self.cursor_key_id < 46:  #24-44 sos白名单编辑
        #         key = EventMesh.publish("persistent_config_get", "whitelist")
        #         if key == None:
        #             key = ["","","","","","","","","","","","","","","","","","","",""]
        #         if self.input_phone_number == "":
        #             if self.language == "zh":
        #                 EventMesh.publish("window_show","请输入号码")
        #             else:
        #                 EventMesh.publish("window_show","Please enter the number")
        #         else:
        #             key[self.cursor_key_id-26]=self.input_phone_number
        #             EventMesh.publish("persistent_config_store",{"whitelist": [key[0],key[1],key[2],key[3],\
        #             key[4],key[5],key[6],key[7],key[8],key[9],key[10],key[11],key[12],key[13],key[14],key[15],\
        #             key[16],key[17],key[18],key[19]]})
        #             if self.language == "zh":
        #                 EventMesh.publish("window_show","设置成功")
        #             else:
        #                 EventMesh.publish("window_show","Set successfully")
        #             print("SOS白名单设置成功")
        #             EventMesh.publish("load_screen", {"screen": "sos_white_list"})
        elif self.cursor_key_id == 47: #闹钟时间设置
            if len(self.input_phone_number)<5:
                EventMesh.publish("window_show","请输入4位数字" if self.language=="zh" else "Enter time of 4 Digit")
            else:
                EventMesh.publish("window_show","设置成功" if self.language=="zh" else "Set successfully")
                self.Alarm_Clock_list = EventMesh.publish("persistent_config_get", "Alarm_Clock_list")
                print("编辑后闹钟时间：",self.input_phone_number)
                self.Alarm_Clock_list[self.Alarm_Clock_num][2]=self.input_phone_number
                self.Alarm_Clock_list[self.Alarm_Clock_num][0]="1"
                if self.Alarm_Clock_num==-1:
                    meta_info={"Alarm_Clock_num":6}
                else:
                    meta_info={"Alarm_Clock_num":self.Alarm_Clock_num}
                EventMesh.publish("persistent_config_store", {"Alarm_Clock_list":self.Alarm_Clock_list})
                EventMesh.publish("set_next_alarm")
                EventMesh.publish("load_screen", {"screen": "Add_alarm_clock","meta_info":meta_info})
        elif self.cursor_key_id == 48: #热点ssid设置
            self.hostpot_ssid_num = self.input_phone_number
            if self.language == "zh":
                EventMesh.publish("window_show","配置成功")
            else:
                EventMesh.publish("window_show","Successfully configured")
            EventMesh.publish("load_screen", {"screen": "hotspot_edit"})
        
        elif self.cursor_key_id == 49: #热点密码设置
            self.hostpot_password_num = self.input_phone_number
            msg = {"SSID": self.hostpot_ssid_num, "pwd": self.hostpot_password_num}
            EventMesh.publish("wifi_config", msg)
            EventMesh.publish("update_screen_wifi", 1)
            EventMesh.publish("load_screen", {"screen": "hotspotsetting"})
                
    def get_hostpot_ssid_num(self,topic=None,msg=None): 
        return [self.hostpot_ssid_num, self.hostpot_password_num]
        
    def input_transfer_setting(self):
        self.language = EventMesh.publish("persistent_config_get", "language")
        value = EventMesh.publish("persistent_config_get", "calltransfer")
        if value == None:
            enable = 0
        else:
            enable = int(value[0])
        if self.language == "zh":
            if self.cursor_key_id-4==0:
                transfer_txt = "Unconditional transfer"
            elif self.cursor_key_id-4==1:
                transfer_txt = "If busy transfer"
            elif self.cursor_key_id-4==2:
                transfer_txt = "Unresponsive transfer"
            else:
                transfer_txt = "Unreachable transfer"
            EventMesh.publish("window_show","open"+transfer_txt+"...")
            if enable == 1:
                if voiceCall.setFw(int(value[1]), 0, value[2]) == 0:
                    print("cancel call transfer ",int(value[1])," success")
                else:
                    print("cancel call transfer ",int(value[1])," failed")
            utime.sleep(1)
            mode=str(self.cursor_key_id-4)
            if voiceCall.setFw(self.cursor_key_id-4, 1,self.input_phone_number)==0:
                EventMesh.publish("persistent_config_store",{"calltransfer": ["1",mode,self.input_phone_number]})
                EventMesh.publish("window_show",transfer_txt+"Successfully saved")
                utime.sleep(1)
                EventMesh.publish("load_screen", {"screen": "calltransfer"})
            else:
                EventMesh.publish("window_show",transfer_txt+"Save failed")
                utime.sleep(1)
                EventMesh.publish("load_screen", {"screen": "calltransfer"})
        else:
            if self.cursor_key_id-4==0:
                transfer_txt = "Unconditional transfer"
            elif self.cursor_key_id-4==1:
                transfer_txt = "Transfer in a hurry"
            elif self.cursor_key_id-4==2:
                transfer_txt = "Unresponsive transfer"
            else:
                transfer_txt = "Non-reachable transfer"
            EventMesh.publish("window_show","Open"+transfer_txt+"...")
            if enable == 1:
                if voiceCall.setFw(int(value[1]), 0, value[2]) == 0:
                    print("cancel call transfer ",int(value[1])," success")
                else:
                    print("cancel call transfer ",int(value[1])," failed")
            utime.sleep(1)
            mode=str(self.cursor_key_id-4)
            if voiceCall.setFw(self.cursor_key_id-4, 1,self.input_phone_number)==0:
                EventMesh.publish("persistent_config_store",{"calltransfer": ["1",mode,self.input_phone_number]})
                EventMesh.publish("window_show",transfer_txt+"Save successfully")
                utime.sleep(1)
                EventMesh.publish("load_screen", {"screen": "calltransfer"})
            else:

                EventMesh.publish("window_show",transfer_txt+"Save failed")
                utime.sleep(1)
                EventMesh.publish("load_screen", {"screen": "calltransfer"})

    def input_pin_setting(self):
        self.language = EventMesh.publish("persistent_config_get", "language")
        phone_number_len = len(self.input_phone_number)
        if phone_number_len < 4:
            if self.language == "zh":
                EventMesh.publish("window_show","请输入4位PIN码")
            else:
                EventMesh.publish("window_show","Please Enter 4-Digit PIN")
        else:
            if self.cursor_key_id == 9:   #关闭PIN码
                if sim.disablePin(self.input_phone_number) == 0:    #调用接口关闭PIN
                    if self.language == "zh":
                        EventMesh.publish("window_show","关闭PIN码成功")
                    else:
                        EventMesh.publish("window_show","The PIN code was Closed successfully")
                    EventMesh.publish("persistent_config_store",{"pin": "0"})
                    EventMesh.publish("persistent_config_store",{"PinCode": ""})
                    EventMesh.publish("about_set_pin_status","0")
                    utime.sleep(1)
                    EventMesh.publish("load_screen", {"screen": "pin"})
                    return
                else:
                    if self.language == "zh":
                        EventMesh.publish("window_show","关闭PIN码失败")
                    else:
                        EventMesh.publish("window_show","Failed to close the PIN")
            elif self.cursor_key_id == 10:     #开启PIN码
                if len(self.pin_current_number) == 0:
                    self.pin_current_number = self.input_phone_number
                    if self.language == "zh":
                        self.call_viwe_options_label.set_text("保存")
                        self.call_viwe_title_label.set_text("请重新输入")
                    else:
                        self.call_viwe_options_label.set_text("Save")
                        self.call_viwe_title_label.set_text("Please re-enter")
                else:
                    if self.pin_current_number==self.input_phone_number:
                        if sim.enablePin(self.input_phone_number) == 0:
                            if self.language == "zh":
                                EventMesh.publish("window_show","开启PIN码成功")
                            else:
                                EventMesh.publish("window_show","The PIN code is turned on successfully")
                            EventMesh.publish("persistent_config_store",{"pin": "1"})
                            EventMesh.publish("persistent_config_store",{"PinCode": self.input_phone_number})
                            EventMesh.publish("about_set_pin_status","1")
                            utime.sleep(1)
                            EventMesh.publish("load_screen", {"screen": "pinrestart"})
                        else:
                            if self.language == "zh":
                                self.call_viwe_options_label.set_text("确定")
                                EventMesh.publish("window_show","开启PIN码失败")
                                self.call_viwe_title_label.set_text("请输入当前PIN码")
                            else:
                                self.call_viwe_options_label.set_text("Sure")
                                EventMesh.publish("window_show","Failed to enable PIN code")
                                self.call_viwe_title_label.set_text("Please enter current PIN")
                            self.pin_puk_count = sim.getPinRemAttempts()
                            pin_cnt = self.pin_puk_count[0]
                            print("pin_cnt=", pin_cnt)
                            if pin_cnt == 0:
                                self.cursor_key_id = 13
                                self.style_18_or_20 = 1
                                self.pin_current_step = 0
                                self.puk_verify_number = ""
                                self.pin_verify_number = ""
                                self.pin_current_number = ""
                                self.display_create()
                    else:
                        if self.language == "zh":
                            EventMesh.publish("window_show","两次PIN码不一致")
                            self.call_viwe_title_label.set_text("请输入当前PIN码")
                        else:
                            EventMesh.publish("window_show","The PIN code does not match twice")
                            self.call_viwe_title_label.set_text("Please enter current PIN")
                    self.pin_current_number = ""
            elif self.cursor_key_id == 11:          #更改PIN码
                if len(self.pin_verify_number)==0:
                    if len(self.input_phone_number) > 3:
                        self.pin_verify_number = self.input_phone_number
                        if self.language == "zh":
                            self.call_viwe_title_label.set_text("请输入新PIN码")
                        else:
                            self.call_viwe_title_label.set_text("Please enter new PIN")
                    else:
                        if self.language == "zh":
                            self.call_viwe_title_label.set_text("请输入当前PIN码")
                            EventMesh.publish("window_show","校验PIN码失败")
                        else:
                            self.call_viwe_title_label.set_text("Please enter current PIN")
                            EventMesh.publish("window_show","PIN verification failed")
                else:
                    if len(self.pin_current_number) == 0:
                        self.pin_current_number = self.input_phone_number
                        if self.language == "zh":
                            self.call_viwe_options_label.set_text("保存")
                            self.call_viwe_title_label.set_text("请重新输入")
                        else:
                            self.call_viwe_options_label.set_text("Save")
                            self.call_viwe_title_label.set_text("Please re-enter")
                    else:
                        if self.pin_current_number==self.input_phone_number:
                            if sim.changePin(self.pin_verify_number,self.input_phone_number) == 0:
                                if self.language == "zh":
                                    EventMesh.publish("window_show","修改PIN码成功")
                                else:
                                    EventMesh.publish("window_show","The PIN code was modified successfully")
                                EventMesh.publish("persistent_config_store",{"pin": "1"})
                                EventMesh.publish("persistent_config_store",{"PinCode": self.input_phone_number})
                                EventMesh.publish("about_set_pin_status","1")
                                utime.sleep(1)
                                EventMesh.publish("load_screen", {"screen": "pinrestart"})
                            else:
                                if self.language == "zh":
                                    EventMesh.publish("window_show","修改PIN码失败")
                                    self.call_viwe_options_label.set_text("确定")
                                    self.call_viwe_title_label.set_text("请输入当前PIN码")
                                else:
                                    EventMesh.publish("window_show","Failed to modify PIN")
                                    self.call_viwe_options_label.set_text("Sure")
                                    self.call_viwe_title_label.set_text("Please enter current PIN")
                        else:
                            if self.language == "zh":
                                EventMesh.publish("window_show","两次PIN码不一致")
                                self.call_viwe_title_label.set_text("请输入当前PIN码")
                            else:
                                EventMesh.publish("window_show","The PIN code does not match twice")
                                self.call_viwe_title_label.set_text("Please enter current PIN")
                        self.pin_current_number = ""
        self.input_phone_number = ""
        self.update_phone_number(None, self.input_phone_number)

    def btn_symbol_click(self, number):
        print("KeypadInput:btn_symbol_click",number)
        self.btn_num_click(number)
    def btn_num_click(self, number):
        """拨号"""
        self.stop_timeout_timer()

        # 检查禁拨号码管理界面的输入模式状态
        if (self.cursor_key_id == "blocked_add" or self.cursor_key_id == "blocked_delete") and not self.input_mode_active:
            # 输入模式未激活，不响应数字键输入
            if self.language == "zh":
                EventMesh.publish("window_show", "请先点击按钮激活输入模式")
            else:
                EventMesh.publish("window_show", "Please click button to activate input mode")
            return

        if isinstance(self.cursor_key_id, int) and self.cursor_key_id >= 9 and self.cursor_key_id <= 12:          #校验PIN码
            print("PIN码相关")
            if len(self.input_phone_number) < 8:
                self.input_phone_number += number
                self.update_phone_number(None, self.input_phone_number)
            else:
                if self.language == "zh":
                    EventMesh.publish("window_show","请输入4-8位PIN码")
                else:
                    EventMesh.publish("window_show","Please Enter 4-8-Digit PIN")
        elif self.cursor_key_id == 13:          #校验PUK码
            if len(self.input_phone_number) < 8:
                self.input_phone_number += number
                self.update_phone_number(None, self.input_phone_number)
            else:
                EventMesh.publish("window_show","输入已达上限" if self.language=="zh" else "Limit reached")
        elif isinstance(self.cursor_key_id, int) and self.cursor_key_id > 25 and self.cursor_key_id < 46:         #sos白名单编辑
            if len(self.input_phone_number) < 20:
                self.input_phone_number += number
                self.update_phone_number(None, self.input_phone_number)
            else:
                if self.language == "zh":
                    EventMesh.publish("window_show","输入已达上限")
                else:
                    EventMesh.publish("window_show","Input has reached the upper limit")
        elif self.cursor_key_id == 24:
            if len(self.input_phone_number)<20:
                self.input_phone_number += number
                self.update_phone_number(None, self.input_phone_number)
            else:
                EventMesh.publish("window_show","输入已达上限" if self.language=="zh" else "Limit reached")
            return
        elif self.cursor_key_id == 25:
            if len(self.input_phone_number)<3:
                self.input_phone_number += number
                self.update_phone_number(None, self.input_phone_number)
            else:
                EventMesh.publish("window_show","输入已达上限" if self.language=="zh" else "Limit reached")
            return       
        elif self.cursor_key_id == 47:#闹钟时间编辑界面
            if len(self.input_phone_number)<5:
                if number=="#" or number=="*":
                    EventMesh.publish("window_show","请输入数字" if self.language=="zh" else "Please enter number")
                else:
                    if len(self.input_phone_number)==0:
                        if int(number)>2:
                            EventMesh.publish("window_show","请输入0-2数字" if self.language=="zh" else "Please enter 0-2 digital")
                        else:
                            self.input_phone_number += number
                    elif len(self.input_phone_number)==1:
                        if self.input_phone_number=="0" or self.input_phone_number=="1":
                            self.input_phone_number = self.input_phone_number+number+":"
                        else:
                            if int(number)>3:
                                EventMesh.publish("window_show","请输入0-2数字" if self.language=="zh" else "Please enter 0-2 digital")

                            else:
                                self.input_phone_number = self.input_phone_number+number+":"
                    elif len(self.input_phone_number)==3:
                        if int(number)>5:
                            EventMesh.publish("window_show","请输入0-5数字" if self.language=="zh" else "Please enter 0-5 digital")
                        else:
                            self.input_phone_number += number
                    else:
                        self.input_phone_number += number
                    print("self.input_phone_number:",self.input_phone_number)
                    self.update_phone_number(None, self.input_phone_number)
            else:
                EventMesh.publish("window_show","输入已达上限" if self.language=="zh" else "Limit reached")
            return 
        else:
            if len(self.input_phone_number) < 20:
                if isinstance(self.cursor_key_id, int) and self.cursor_key_id >= 14 and self.cursor_key_id<=21:
                    if len(self.input_phone_number)<15:
                        self.input_phone_number += number
                        self.update_phone_number(None, self.input_phone_number)
                    else:
                        EventMesh.publish("window_show","输入已达上限" if self.language=="zh" else "Limit reached")

                else:
                    self.input_phone_number += number
                    self.update_phone_number(None, self.input_phone_number)

                # 特殊代码检测 - 在字符添加之后，在长度检查内部
                if self.cursor_key_id == 8 or self.cursor_key_id == "8":
                    print("DEBUG: 进入特殊代码检测, input_phone_number='{}'".format(self.input_phone_number))
                    if self.input_phone_number == "*#06#":
                        self.set_style_20()
                        EventMesh.publish("load_screen", {"screen": "factory_test","meta_info":"0"})
                    elif self.input_phone_number == "*#0000#":
                        self.set_style_20()
                        EventMesh.publish("load_screen", {"screen": "phoneinfo","meta_info":"1"})
                    elif self.input_phone_number == "*#83780#":
                        self.set_style_20()
                        EventMesh.publish("load_screen", {"screen": "factory_test","meta_info":"2"})
                    elif self.input_phone_number == "*#68681#":
                        self.set_style_20()
                        EventMesh.publish("uart_to_usb")
                        EventMesh.publish("load_screen", {"screen": "main"})
                    elif self.input_phone_number == "*#68682#":
                        self.set_style_20()
                        EventMesh.publish("usb_to_uart")
                        EventMesh.publish("load_screen", {"screen": "main"})
                    elif self.input_phone_number == "*#68683#":  #中英文切换
                        print("切换成英文")
                        self.set_style_20()
                        self.language = EventMesh.publish("persistent_config_get", "language")
                        if self.language == "en":
                            EventMesh.publish("persistent_config_store",{"language": "zh"})  #中文
                            self.language = "zh"
                            print("language:",self.language)
                        else:
                            EventMesh.publish("persistent_config_store",{"language": "en"})  #英文
                            self.language = "en"
                            print("language:",self.language)
                        EventMesh.publish("load_screen", {"screen": "main"})
                    elif self.input_phone_number == "*#68684#":  #禁拨号码管理
                        self.set_style_20()
                        EventMesh.publish("load_screen", {"screen": "blocked_numbers_main"})
                    # elif self.input_phone_number == "*#110#0000#":
                    #     print("进入SOS界面")
                    #     EventMesh.publish("load_screen", {"screen": "SOS_setting"})
            
    def get_cur_number_len(self,topic=None,msg=None):        
        if len(self.input_phone_number) > 0 :
            self.num_len = 1
        elif len(self.input_phone_number) == 0:
            self.num_len = 0        
        return self.num_len       
    
    def reset_input_number(self,topic=None,msg=None): 
        self.input_phone_number = ""

    def btn_back_click(self):
        """退格至空后返回待机页面"""
        self.stop_timeout_timer()
        self.backspace_phone_number()

    def btn_pwk_click(self):
        self.stop_timeout_timer()
        self.language = EventMesh.publish("persistent_config_get", "language")
        if self.cursor_key_id == 12 :
            if self.language == "zh":
                EventMesh.publish("window_show","请输入PIN码")
            else:
                EventMesh.publish("window_show","Please enter PIN")
        elif self.cursor_key_id == 13:
            if self.language == "zh":
                EventMesh.publish("window_show","请输入PUK码")
            else:
                EventMesh.publish("window_show","Please enter PUK")
        else:
            EventMesh.publish("set_handfree_state", 0)
            EventMesh.publish("load_screen", {"screen": "main"})
        self.set_style_20()
        return True
    
    def btn_num_long_click(self,number):
        """进入呼叫页面"""
        self.stop_timeout_timer()
        key = EventMesh.publish("persistent_config_get", "speed_dial_number")
        if self.cursor_key_id == 12 :
            if self.language == "zh":
                EventMesh.publish("window_show","请输入PIN码")
            else:
                EventMesh.publish("window_show","Please enter PIN")
            self.set_style_20()
        elif self.cursor_key_id == 13:
            if self.language == "zh":
                EventMesh.publish("window_show","请输入PUK码")
            else:
                EventMesh.publish("window_show","Please enter PUK")
            self.set_style_20()
        elif self.cursor_key_id == 47:#闹钟时间编辑界面
            return True
        else:
            if number == "*" or number == "#":
                return
            index = int(number)-2
            if index < 0:
                return
            if key == None:
                key = ["","","","","","","",""]
            if key[index] == "":
                EventMesh.publish("load_screen", {"screen":"speed_dial","meta_info":{"flag":"1"}})
                return
            status = sim.getStatus()
            if status == 0:
                if not (key[index]=="110" or key[index]=="112" or key[index] =="119" or key[index] =="120" or key[index] =="122" or key[index] == "911"):
                    if self.language == "zh":
                        EventMesh.publish("window_show", "仅能拨打紧急号码")
                    else:
                        EventMesh.publish("window_show", "Only Emergency Numbers Can Be Dialed")
                    return                
            else:
                EventMesh.publish("load_screen", {"screen": "call_out", "meta_info": ["",key[index]]})

    def btn_call_click(self):
        """呼叫"""
        self.stop_timeout_timer()
        self.language = EventMesh.publish("persistent_config_get", "language")
        if self.cursor_key_id == 12 :
            if self.language == "zh":
                EventMesh.publish("window_show","请输入PIN码")
            else:
                EventMesh.publish("window_show","Please enter PIN")
            self.set_style_20()
        elif self.cursor_key_id == 13:
            if self.language == "zh":
                EventMesh.publish("window_show","请输入PUK码")
            else:
                EventMesh.publish("window_show","Please enter PUK")
            self.set_style_20()
        elif self.cursor_key_id == 8:
            status = sim.getStatus()
            if status == 0:
                if not (self.input_phone_number=="110" or self.input_phone_number=="112" or self.input_phone_number=="119" or self.input_phone_number=="120" or self.input_phone_number=="122" or self.input_phone_number == "911"):
                    if self.language == "zh":
                        EventMesh.publish("window_show", "仅能拨打紧急号码")
                    else:
                        EventMesh.publish("window_show", "Only Emergency Numbers Can Be Dialed")
                    return 
            if self.input_phone_number=="":
                EventMesh.publish("window_show", "请输入号码" if self.language=="zh" else "Please enter number")
                return 
            # if self.input_phone_number[0] == "*" or self.input_phone_number[0] == "#":
            if (self.input_phone_number[0:4] in ["*92*", "*93*", "*94*", "*95*", "*96*", "*97*", "*91*", "*77*", "#92#","#93#", "#94#", "#95#", "#96#", "#97#", "#91#", "#77#", "*56*", "*700","*701","*59#","#56#","#59#","*728","*62*", "*63*","*64*"]) or (self.input_phone_number[0:3] == "*76") or (self.input_phone_number[0:5] in ["*727#", " *#56*#" ,"*56*#"]):
                EventMesh.publish("load_screen", {"screen": "ussd_screen","meta_info":self.input_phone_number})
            else:
                # 检查是否为禁拨号码
                blocked_numbers = EventMesh.publish("persistent_config_get", "blocked_numbers")
                if blocked_numbers is None:
                    blocked_numbers = []

                if self.input_phone_number in blocked_numbers:
                    if self.language == "zh":
                        EventMesh.publish("window_show", "此号码禁止拨打")
                    else:
                        EventMesh.publish("window_show", "This number is blocked")
                    return

                name = EventMesh.publish("get_phone_number_name", self.input_phone_number)
                EventMesh.publish("load_screen", {"screen": "call_out", "meta_info": [name, self.input_phone_number]})
            self.set_style_20()
        elif self.cursor_key_id == 47:#闹钟时间编辑界面
            return True

    def btn_redial_click(self):
        #重拨按键
        self.stop_timeout_timer()
        self.language = EventMesh.publish("persistent_config_get", "language")
        call_logs_list = EventMesh.publish("persistent_config_get", "call_logs_list")
        if self.cursor_key_id == 12 :
            if self.language == "zh":
                EventMesh.publish("window_show","请输入PIN码")
            else:
                EventMesh.publish("window_show","Please enter PIN")
            self.set_style_20()
        elif self.cursor_key_id == 13:
            if self.language == "zh":
                EventMesh.publish("window_show","请输入PUK码")
            else:
                EventMesh.publish("window_show","Please enter PUK")
            self.set_style_20()
        elif self.cursor_key_id == 47:#闹钟时间编辑界面
            return True
        else:
            self.dialed_call_menu_list = [sublist for sublist in call_logs_list if sublist[0] == 2]
            self.count = len(self.dialed_call_menu_list)
            if self.count == 0:
                if self.language == "zh":
                    EventMesh.publish("window_show", "已拨记录为空")
                else:
                    EventMesh.publish("window_show", "The dialed record is empty")
            else:
                status = sim.getStatus()
                if status == 0:
                    if not (self.dialed_call_menu_list[0][2]=="110" or self.dialed_call_menu_list[0][2]=="112" or self.dialed_call_menu_list[0][2]=="119" or self.dialed_call_menu_list[0][2]=="120" or self.dialed_call_menu_list[0][2]=="122" or self.dialed_call_menu_list[0][2] == "911"):
                        if self.language == "zh":
                            EventMesh.publish("window_show", "仅能拨打紧急号码")
                        else:
                            EventMesh.publish("window_show", "Only Emergency Numbers Can Be Dialed")
                        return  
                EventMesh.publish("load_screen", {"screen": "call_out", "meta_info": [self.dialed_call_menu_list[0][1], self.dialed_call_menu_list[0][2]]})
                
    def btn_hands_free_click(self,msg):
        self.stop_timeout_timer()
        self.language = EventMesh.publish("persistent_config_get", "language")
        if self.cursor_key_id == 12 :
            if self.language == "zh":
                EventMesh.publish("window_show","请输入PIN码")
            else:
                EventMesh.publish("window_show","Please enter PIN")
            self.set_style_20()
            return True
        elif self.cursor_key_id == 13:
            if self.language == "zh":
                EventMesh.publish("window_show","请输入PUK码")
            else:
                EventMesh.publish("window_show","Please enter PUK")
            self.set_style_20()
            return True
        elif self.cursor_key_id == 47:#闹钟时间编辑界面
            return True
        else:
            return False

    def btn_hook_click(self,msg):
        self.stop_timeout_timer()
        self.language = EventMesh.publish("persistent_config_get", "language")
        if self.cursor_key_id == 12 :
            if self.language == "zh":
                EventMesh.publish("window_show","请输入PIN码")
            else:
                EventMesh.publish("window_show","Please enter PIN")
            self.set_style_20()
            return True
        elif self.cursor_key_id == 13:
            if self.language == "zh":
                EventMesh.publish("window_show","请输入PUK码")
            else:
                EventMesh.publish("window_show","Please enter PUK")
            self.set_style_20()
            return True
        elif self.cursor_key_id == 47:#闹钟时间编辑界面
            return True
        else:
            return False
        
    def btn_headset_click(self):
        self.stop_timeout_timer()
        self.language = EventMesh.publish("persistent_config_get", "language")
        if self.cursor_key_id == 12 :
            if self.language == "zh":
                EventMesh.publish("window_show","请输入PIN码")
            else:
                EventMesh.publish("window_show","Please enter PIN")
            self.set_style_20()
            return True
        elif self.cursor_key_id == 13:
            if self.language == "zh":
                EventMesh.publish("window_show","请输入PUK码")
            else:
                EventMesh.publish("window_show","Please enter PUK")
            self.set_style_20()
            return True
        elif self.cursor_key_id == 47:#闹钟时间编辑界面
            return True

    def btn_fast_click(self,No):
        self.stop_timeout_timer()
        self.language = EventMesh.publish("persistent_config_get", "language")
        if self.cursor_key_id == 12 :
            if self.language == "zh":
                EventMesh.publish("window_show","请输入PIN码")
            else:
                EventMesh.publish("window_show","Please enter PIN")
            self.set_style_20()
            return True
        elif self.cursor_key_id == 13:
            if self.language == "zh":
                EventMesh.publish("window_show","请输入PUK码")
            else:
                EventMesh.publish("window_show","Please enter PUK")
            self.set_style_20()
            return True
        elif self.cursor_key_id == 47:#闹钟时间编辑界面
            return True

    def stop_timeout_timer(self):
        if self.cursor_key_id == 8:
            if self.tone_timeout_timer_runing==1:
                self.tone_timeout_timer.stop()
                self.tone_timeout_timer_runing = 0
            EventMesh.publish("howler_tone_control",0)

    def stop_tone_to_howler(self,args):
        EventMesh.publish("audio_tone_stop")
        EventMesh.publish("howler_tone_control",1)
        self.tone_timeout_timer.start(30000, 0, self.stop_howler_to_main)
    
    def stop_howler_to_main(self,args):
        EventMesh.publish("howler_tone_control",0)
        # EventMesh.publish("load_screen", {"screen": "main"})

    def btn_hook_click(self,msg):
        EventMesh.publish("howler_tone_control",0)
        if self.cursor_key_id == 10 and "KT26C_HJ" in EventMesh.publish("get_sw_version"):
            return True
        self.language = EventMesh.publish("persistent_config_get", "language")
        if self.cursor_key_id == 12 :
            if self.language == "zh":
                EventMesh.publish("window_show","请输入PIN码")
            else:
                EventMesh.publish("window_show","Please enter PIN")
            self.set_style_20()
            return True
        elif self.cursor_key_id == 13:
            if self.language == "zh":
                EventMesh.publish("window_show","请输入PUK码")
            else:
                EventMesh.publish("window_show","Please enter PUK")
            self.set_style_20()
            return True
        elif self.cursor_key_id == 47:#闹钟时间编辑界面
            return True
        else:
            return False
        
class DialOptionScreen(Screen):
    """
    拨号选项页面
    """
    NAME = "dialoption"

    def __init__(self):
        super().__init__()
        self.meta = dial_options_screen
        self.cur = 0
        self.count = 2
        self.number = 0
        self.dial_options_list = dial_options_list
        self.dial_options_select_label = dial_options_select_label
        self.dial_options_back_label = dial_options_back_label
        self.btn_list = []
        self.currentButton = None
        self.language = "zh"
        self.dial_options_menu_list = [
            "1 呼叫",
            "2 添加联系人"
        ]
        self.dial_options_menu_list_en = [
            "1 Call",
            "2 Add Contact"
        ]

    def initialization(self):
        self.btn_list = []
        if self.meta_info != None:
            self.number = self.meta_info.get("number") 
        self.list_create()
        return True

    def list_create(self):
        self.dial_options_list.delete()
        self.dial_options_list = lv.list(self.meta)
        self.dial_options_list.set_pos(0, 2)
        self.dial_options_list.set_size(128, 48)
        self.dial_options_list.set_style_pad_left(0, 0)
        self.dial_options_list.set_style_pad_top(0, 0)
        self.dial_options_list.set_style_pad_row(0, 0)
        self.dial_options_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
        self.dial_options_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR)
        self.dial_options_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.SCROLLED)
        self.language = EventMesh.publish("persistent_config_get", "language")
        if self.language == "zh":
            self.option=self.dial_options_menu_list
            self.dial_options_select_label.set_size(48, 12)
            self.dial_options_select_label.set_text("选择")
            self.dial_options_back_label.set_size(48, 12)
            self.dial_options_back_label.set_pos(104, 51)
            self.dial_options_back_label.set_text("返回")
        else:
            self.option=self.dial_options_menu_list_en
            self.dial_options_select_label.set_size(48, 12)
            self.dial_options_select_label.set_text("Select")
            self.dial_options_back_label.set_size(48, 12)
            self.dial_options_back_label.set_pos(104, 51)
            self.dial_options_back_label.set_text("Back")
        for idx, item in enumerate(self.option):
            dial_options_list_btn = lv.btn(self.dial_options_list)
            dial_options_list_btn.set_pos(0, 0)
            dial_options_list_btn.set_size(115, 16)
            dial_options_list_btn.add_style(style_list_black, lv.PART.MAIN | lv.STATE.DEFAULT)
            dial_options_list_label = lv.label(dial_options_list_btn)
            dial_options_list_label.set_pos(1, 2)
            dial_options_list_label.set_size(115, 16)
            dial_options_list_label.set_text(item)
            self.btn_list.append((dial_options_list_btn, dial_options_list_label))

        self.add_state()

    def add_state(self, cur=None):
        if cur is None:
            cur = self.cur
        self.currentButton = self.dial_options_list.get_child(cur)
        self.currentButton.set_style_bg_color(lv.color_make(0xff, 0xff, 0xff), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.set_style_bg_grad_color(lv.color_make(0xff, 0xff, 0xff), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.btn_list[cur][1].set_style_text_color(lv.color_make(0x00, 0x00, 0x00), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.scroll_to_view(lv.ANIM.OFF)

    def clear_state(self, cur=None):
        if cur is None:
            cur = self.cur
        self.currentButton = self.dial_options_list.get_child(cur)
        self.currentButton.set_style_bg_color(lv.color_make(0x00, 0x00, 0x00), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.set_style_bg_grad_color(lv.color_make(0x00, 0x00, 0x00), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.btn_list[cur][1].set_style_text_color(lv.color_make(0xff, 0xff, 0xff), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.scroll_to_view(lv.ANIM.OFF)

    def btn_menu_click(self):
        # 选择按键
        self.language = EventMesh.publish("persistent_config_get", "language")
        if self.cur == 0:
            #呼叫
            name = self.meta_info.get("name")
            if name==None:
                name = ""
            status = sim.getStatus()
            if status == 0:
                if not (self.number=="110" or self.number=="112" or self.number=="119" or self.number=="120" or self.number=="122" or self.number == "911"):
                    if self.language == "zh":
                        EventMesh.publish("window_show", "仅能拨打紧急号码")
                    else:
                        EventMesh.publish("window_show", "Only Emergency Numbers Can Be Dialed")
                    return                
            if self.number == "":
                if self.language == "zh":
                    EventMesh.publish("window_show","号码为空")
                else:
                    EventMesh.publish("window_show","The Number Is Empty")
            # elif self.number[0] == "*" or self.number[0] == "#":
            if (self.number[0:4] in ["*92*", "*93*", "*94*", "*95*", "*96*", "*97*", "*91*", "*77*", "#92#","#93#", "#94#", "#95#", "#96#", "#97#", "#91#", "#77#", "*56*", "*700","*701","*59#","#56#","#59#","*728","*62*", "*63*","*64*"]) or (self.number[0:3] == "*76") or (self.number[0:5] in ["*727#", " *#56*#" ,"*56*#"]):
                EventMesh.publish("load_screen", {"screen": "ussd_screen","meta_info":self.number})
            else:
                EventMesh.publish("load_screen", {"screen": "call_out", "meta_info": [name,self.number]})         
        elif self.cur == 1:
            #添加联系人
            if self.number == "":
                if self.language == "zh":
                    EventMesh.publish("window_show","号码为空")
                else:
                    EventMesh.publish("window_show","The Number Is Empty")
            else:
                meta_info = self.meta_info
                meta_info["prev_screen"] = "21"
                EventMesh.publish("load_screen", {"screen": "phone_book_store_select","meta_info":meta_info})
                
                # EventMesh.publish("load_screen", {"screen": "NewMessageScreen", "meta_info":{"prev_screen": "8", "number": self.number}})   
        
    def btn_back_click(self):
        # 返回按键
        if self.cur > 0:
            self.clear_state()
            self.cur = 0
        EventMesh.publish("load_screen", {"screen": "keypad_input","meta_info":["8", self.number]})

    def btn_up_click(self):
        self.clear_state()
        self.cur = self.prev_idx(self.cur, self.count)
        self.add_state()

    def btn_down_click(self):
        self.clear_state()
        self.cur = self.next_idx(self.cur, self.count)
        self.add_state()

class FactoryTestWindows(Screen):
    """欢迎页面"""
    NAME = "factory_test"

    def __init__(self):
        super().__init__()
        self.meta = factory_test_screen
        self._label1 = factory_test_label1
        self._label2 = factory_test_label2
        self._label3 = factory_test_label3
        self._label4 = factory_test_label4
        self._label5 = factory_test_label5
        
        self.cur = 0
        self.flash = 0
        self.timer_flag = 0
        self.flash_count = 0
        self.cursor_key_id = 0
        self.update_screen_timer = osTimer()
        self.ethernet_test_timer = osTimer()
    def post_processor_after_instantiation(self):
        EventMesh.subscribe("stop_update_screen", self.stop_update_screen)

    def initialization(self):
        self.cur = 0
        self.flash = 0
        self.timer_flag = 0
        self.flash_count = 0
        self.gpio18 = 0
        self.key1 = "U D L R  SEL DEL RC"
        self.key2 = "1  2  3  CALL PW"
        self.key3 = "4  5  6  F1   F2"
        self.key4 = "7  8  9  F3   F4"
        self.key5 = "*  0  #  SMS  SPK"
        self.cursor_key_id = int(self.meta_info)
        if self.cursor_key_id==0:
            if self.timer_flag == 1:
                self.update_screen_timer.stop()
            self.timer_flag = 0  
            self.display_imei()
        elif self.cursor_key_id==2:
            self.test_screen_create()
        return True

    def display_imei(self):
        self._label1.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
        self._label1.set_text("IMEI")
        self._label1.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
        imei = modem.getDevImei()
        if imei != -1:
            self._label2.set_text("IMEI:"+imei)
        else:
            self._label2.set_text("IMEI:wei zhu ma")
        self._label3.set_text("")
        self._label4.set_text("")
        self._label5.set_text("")
        # if self.timer_flag == 1:
        #     self.update_screen_timer.stop()

    def test_screen_create(self):
        self._label1.set_style_text_align(lv.TEXT_ALIGN.CENTER, 0)
        self._label3.set_text("")
        self._label4.set_text("")
        if self.cur == 0:
            self._label1.set_text("xinhao jianshi")
            signal= net.getSignal(0)
            self._label2.set_text("Rssi:"+str(signal[1][0])+" dbm")
            self._label3.set_text("Rsrp:"+str(signal[1][1])+" dbm")
            self._label4.set_text("")
            self._label5.set_text("")
        elif self.cur == 1:
            self._label1.set_text("dianchi dianliang")
            self.report_battery_temperature()
            voltage = int(sum([Power.getVbatt() for i in range(100)]) / 100)
            self._label2.set_text("Voltage:"+str(voltage)+" mV")
            self._label3.set_text("")
            if EventMesh.publish("get_charging_state"):
                self._label4.set_text("chongdian yilianjie")
            else:
                self._label4.set_text("chongdian weilianji")
            self._label5.set_text("")
                
        elif self.cur == 2:
            self._label1.set_text("ling sheng")
            self._label2.set_text("")
            self._label3.set_text("jian ce zhong...")
            self._label4.set_text("")
            self._label5.set_text("")
            EventMesh.publish("audio_play_stop")
            EventMesh.publish("audio_play","U:/static/ring_1.mp3")
        elif self.cur == 3:
            self._label1.set_text("ye jing")
            self._label2.set_text("")
            self._label3.set_text("")
            self._label4.set_text("")
            self._label5.set_text("")
        elif self.cur == 4:
            version = EventMesh.publish("get_sw_version")
            self._label1.set_text("Version")
            self._label2.set_text("soft:{}".format(version))
            self._label3.set_text("hard:03KT4(1B)")
            self._label4.set_text("")
            self._label5.set_text("")
        elif self.cur == 5:
            self._label1.set_text("yitaiwang ceshi")
            self._label2.set_text("")
            self._label3.set_text("")
            self._label4.set_text("")
            self._label5.set_text("")
            if EventMesh.publish("persistent_config_get","eth_status")!="1":
                EventMesh.publish("eth_open")
            self.ethernet_test_timer.start(2000, 1, self.ethernet_test_def)
            
        elif self.cur == 6:
            self.ethernet_test_timer.stop()
            # if EventMesh.publish("persistent_config_get","eth_status")=="1":
            #     EventMesh.publish("eth_close",0)
            self._label1.set_text(self.key1)
            self._label1.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
            self._label2.set_text(self.key2)
            self._label3.set_text(self.key3)
            self._label4.set_text(self.key4)
            self._label5.set_text(self.key5)
            self._label5.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
            self.key_isempty()
        elif self.cur == 7:
            self._label1.set_text("")
            self._label2.set_text("")
            self._label3.set_text("")
            self._label4.set_text("")
            self._label5.set_text("")
            self._label5.set_style_text_align(lv.TEXT_ALIGN.RIGHT, 0)
        if self.cur!=6 and self.cur!=7:
            if self.timer_flag == 1:
                self.update_screen_timer.stop()
            self.timer_flag = 1
            if self.cur == 3:
                self.update_screen_timer.start(500, 1, self.get_signal_timer)
            elif self.cur == 0 or self.cur == 1 or self.cur == 2 or self.cur == 4 or self.cur == 5:
                self.update_screen_timer.start(1000, 1, self.get_signal_timer)
        else:
            self.update_screen_timer.stop()

    def ethernet_test_def(self,*args):
        eth_status = EventMesh.publish("get_eth_status")
        dev = eth_status[0]
        active = eth_status[1]
        link = eth_status[2]
        if dev == True:
            self._label2.set_text("wangka shebei OK")
        else:
            self._label2.set_text("wangka shebei ERR")
            
        if active == True:
            self._label3.set_text("wangka jihuo")
        else:
            self._label3.set_text("wangka weijihuo")
            
        if link == True:
            self._label4.set_text("wangka/xian OK")
        else:
            self._label4.set_text("wangka/xian ERR")
        self._label5.set_text("")

    def get_signal_timer(self,*args):
        if self.cursor_key_id == 2:
            if self.cur == 0:
                signal= net.getSignal(0)
                self._label2.set_text("Rssi:"+str(signal[1][0])+" dbm")
                self._label3.set_text("Rsrp:"+str(signal[1][1])+" dbm")
            elif self.cur == 1:
                self.report_battery_temperature()
                voltage = Power.getVbatt()
                self._label2.set_text("Voltage:"+str(voltage)+" mV")
                if EventMesh.publish("get_charging_state"):
                    self._label4.set_text("chongdian yi lianjie")
                else:
                    self._label4.set_text("chongdian wei lianjie")
            elif self.cur == 3:
                if self.flash == 0:
                    self.flash = 1
                    value = 63
                    self.flash_count = self.flash_count+1
                else:
                    self.flash = 0
                    value = 22
                lcd = LCD()
                lcd.lcd_write_cmd(0x81,1)
                lcd.lcd_write_cmd(value,1)
            elif self.cur == 4:
                if self.timer_flag == 1:
                    self.update_screen_timer.stop()  #熄灭
                self.timer_flag == 0
                EventMesh.publish("set_contrast")
                self.gpio18 = Pin(Pin.GPIO38 , Pin.OUT, Pin.PULL_DISABLE, 0)
                print("灭屏？2")
                self.gpio18.write(0)
                utime.sleep(1)
                self.gpio18.write(1)
                utime.sleep(1)
                self.gpio18.write(0)
    def stop_update_screen(self,topic,msg):
        if self.timer_flag == 1:
            print("熄灭")
            self.update_screen_timer.stop()  #熄灭
            EventMesh.publish("set_contrast")
        self.timer_flag == 0    
    def report_battery_temperature(self):
        temperature = EventMesh.publish("get_battery_celsius")
        print("在timer获得：",temperature)
        if temperature == -11:
            self._label3.set_text("C: dian chi bu zai wei...")
        if temperature == -12:
            self._label3.set_text("C: dian chi wen du yi chang")
        if temperature == -13:
            self._label3.set_text("C: --")
        if temperature >= -10 and temperature <= 55:
            self._label3.set_text("C:" + str(temperature) + "she shi du")

    def key_isempty(self):
        if self.cur == 6:
            if self.key1=="                   " and self.key2=="                " and self.key3=="                " and self.key4=="                " and self.key5=="                 ":
                self.cur += 1
                if self.cur == 7:
                    utime.sleep(1)
                    EventMesh.publish("load_screen", {"screen": "reset","meta_info":"1"})
                self.test_screen_create()
            else:
                pass
                
    def btn_down_click(self):
        """向下选择"""
        if self.cursor_key_id == 0:
            return
        else:
            if self.cur == 2:
                EventMesh.publish("audio_play_stop")
            if self.cur == 3:
                EventMesh.publish("set_contrast")
            if self.cur == 5:
                self.ethernet_test_timer.stop()
            if self.cur == 6:
                if self.key1!="                   " or self.key2!="                " or self.key3!="                " or self.key4!="                " or self.key5!="                 ":
                #     self.cur = self.cur+1
                #     self.test_screen_create()
                # else:
                    self.key1 = self.key1.replace("D ","  ")
                    self.test_screen_create()
            # elif self.cur == 6:
            #     # EventMesh.publish("load_screen", {"screen": "reset","meta_info":"1"})
            #     EventMesh.publish("load_screen", {"screen": "reset"})
            else:
                if self.cur > 7:
                    self.cur = 0
                else:
                    self.cur = self.cur+1
                    self.test_screen_create()

    def btn_up_click(self):
        """向上选择"""
        if self.cur == 6:
            if self.key1!="                   " or self.key2!="                " or self.key3!="                " or self.key4!="                " or self.key5!="                 ":
                self.key1 = self.key1.replace("U"," ")
                self.test_screen_create()

    def btn_right_click(self):
        if self.cur == 6:
            self.key1 = self.key1.replace("R ","  ")
            self.test_screen_create()

    def btn_left_click(self):
        if self.cur == 6:
            self.key1 = self.key1.replace(" L","  ")
            self.test_screen_create()
            
    def get_input_method(self):
        return "123"
    
    def btn_symbol_click(self, number):
        print("FactoryTestWindows:btn_symbol_click",number)
        self.btn_num_click(number)
        
    def get_input_method(self):
        return "123"

    def btn_num_click(self, number):
        if self.cur == 6:
            if number=="1" or number=="2" or number=="3":
                self.key2 = self.key2.replace(number," ")
            if number=="4" or number=="5" or number=="6":
                self.key3 = self.key3.replace(number," ")
            if number=="7" or number=="8" or number=="9":
                self.key4 = self.key4.replace(number," ")
            if number=="*" or number=="0" or number=="#":
                self.key5 = self.key5.replace(number," ")
            self.test_screen_create()

    def btn_menu_click(self):
        if self.cur == 6:
            self.key1 = self.key1.replace("SEL","   ")  #确认选择
            self.test_screen_create()

    def btn_redial_click(self):
        if self.cur == 6:
            self.key1 = self.key1.replace("RC","  ")   #重拨
            self.test_screen_create()

    def btn_call_click(self):
        if self.cur == 6:
            self.key2 = self.key2.replace("CALL","    ")      #呼叫
            self.test_screen_create()

    def btn_headset_click(self):
        if self.cur == 6:
            self.key5 = self.key5.replace("SMS","   ")    #短信
            self.test_screen_create()

    def btn_hands_free_click(self, msg):
        if self.cur == 6:
            if msg:
                EventMesh.publish("audio_key_tone",15)
                self.key5 = self.key5.replace("SPK","   ")   #免提
                self.test_screen_create()
        return True

    def btn_fast_click(self, No):
        if self.cur == 6:
            if No == 0:
                self.key3 = self.key3.replace("F1","  ")
            if No == 1:
                self.key3 = self.key3.replace("F2","  ")
            if No == 2:
                self.key4 = self.key4.replace("F3","  ")
            if No == 3:
                self.key4 = self.key4.replace("F4","  ")
            self.test_screen_create()

    def btn_back_click(self):
        # 返回按键
        if self.cur == 2:
            EventMesh.publish("audio_play_stop")
        if self.cur == 3:
            EventMesh.publish("set_contrast")
        if self.cur == 5:
            self.ethernet_test_timer.stop()
        if self.cur == 6:
            if self.key1!="                   " or self.key2!="                " or self.key3!="                " or self.key4!="                " or self.key5!="                 ":
            #     EventMesh.publish("load_screen", {"screen": "main"})
            # else:
                self.key1 = self.key1.replace("DEL","   ")
                self.test_screen_create()
        else:
            if self.timer_flag == 1:
                self.timer_flag = 0
                self.update_screen_timer.stop()
            EventMesh.publish("load_screen", {"screen": "main"})

    def btn_pwk_click(self):
        """呼叫挂断返回通话结束页面"""
        if self.cur == 3:
            EventMesh.publish("set_contrast")
        if self.cur == 5:
            self.ethernet_test_timer.stop()
        if self.cur == 6:
            if self.key1!="                   " or self.key2!="                " or self.key3!="                " or self.key4!="                " or self.key5!="                 ":
            #     EventMesh.publish("load_screen", {"screen": "main"})
            # else:
                self.key2 = self.key2.replace("PW","  ")
                self.test_screen_create()
        else:
            if self.timer_flag == 1:
                self.timer_flag = 0
                self.update_screen_timer.stop()
            EventMesh.publish("load_screen", {"screen": "main"})
        return True

#网络服务菜单
class QuikKeySetting(Screen):
    """
    快捷键设置页面
    """
    NAME = "quickkey"

    def __init__(self):
        super().__init__()
        self.meta = quickkey_screen
        self.cur = 0
        self.count = 4
        self.quickkey_screen_list = quickkey_list
        self.quickkey_screen_title = quickkey_screen_title
        self.quickkey_screen_return = quickkey_screen_return
        self.quickkey_screen_sure = quickkey_screen_sure
        self.key = []
        self.btn_list = []
        self.language = "zh"
        self.currentButton = None
        self.flag = 0
        self.quickkey_menu_list = [
            {

                "screen": "keypad_input",
                "title":" 快捷号码1",
            },
            {
                "screen": "keypad_input",
                "title":" 快捷号码2",
            },
            {
                "screen": "keypad_input",
                "title":" 快捷号码3",
            },
            {
                "screen": "keypad_input",
                "title":" 快捷号码4",
            },
        ]
        self.quickkey_menu_list_en = [
            {

                "screen": "keypad_input",
                "title":" QuickNumber1",
            },
            {
                "screen": "keypad_input",
                "title":" QuickNumber2",
            },
            {
                "screen": "keypad_input",
                "title":" QuickNumber3",
            },
            {
                "screen": "keypad_input",
                "title":" QuickNumber4",
            },
        ]

    def initialization(self):
        self.key = []
        self.btn_list = []
        if self.meta_info != None:
            self.flag = int(self.meta_info.get("flag"))
        self.list_create()
        return True

    def list_create(self):
        self.quickkey_screen_list.delete()
        self.quickkey_screen_list = lv.list(self.meta)
        self.quickkey_screen_list.set_pos(0, 16)
        self.quickkey_screen_list.set_size(128, 32)
        self.quickkey_screen_list.set_style_pad_left(0, 0)
        self.quickkey_screen_list.set_style_pad_top(0, 1)
        self.quickkey_screen_list.set_style_pad_row(0, 0)
        self.quickkey_screen_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
        self.quickkey_screen_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR)
        self.language = EventMesh.publish("persistent_config_get", "language")
        if self.language == "zh":
            self.option=self.quickkey_menu_list
            self.quickkey_screen_title.set_text("快捷设置")
            self.quickkey_screen_return.set_text("返回")
            self.quickkey_screen_sure.set_text("选项")
        else:
            self.option=self.quickkey_menu_list_en
            self.quickkey_screen_title.set_text("Quick Setting")
            self.quickkey_screen_sure.set_size(48, 12)
            self.quickkey_screen_sure.set_text("Select")
            self.quickkey_screen_return.set_pos(104, 51)
            self.quickkey_screen_return.set_text("Back")
        for idx, item in enumerate(self.option):
            quickkey_obj_list_btn = lv.btn(self.quickkey_screen_list)
            quickkey_obj_list_btn.set_pos(0, 1)
            quickkey_obj_list_btn.set_size(115, 16)
            quickkey_obj_list_btn.add_style(style_list_black, lv.PART.MAIN | lv.STATE.DEFAULT)
            quickkey_obj_list_label = lv.label(quickkey_obj_list_btn)
            quickkey_obj_list_label.center()
            quickkey_obj_list_label.set_pos(0, 2)
            quickkey_obj_list_label.set_size(115, 16)
            quickkey_obj_list_label.set_text(item["title"])
            quickkey_obj_list_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
            self.btn_list.append((quickkey_obj_list_btn, quickkey_obj_list_label))


        self.add_state()

    def add_state(self, cur=None):
        self.language = EventMesh.publish("persistent_config_get", "language")
        if cur is None:
            cur = self.cur
        self.key = EventMesh.publish("persistent_config_get", "quikkey")
        if self.key == None:
            self.key = ["","","",""]
        # if len(self.key[self.cur])==0:
        if self.language == "zh":
            self.quickkey_screen_sure.set_text("选项")
            quickkey_list_value.set_pos(25, 51)
            quickkey_list_value.set_size(78, 12)
        else:
            self.quickkey_screen_sure.set_size(48, 12)
            self.quickkey_screen_sure.set_text("Select")
            quickkey_list_value.set_pos(40, 51)
            quickkey_list_value.set_size(60, 12)
        quickkey_list_value.set_text(self.key[self.cur])
        self.currentButton = self.quickkey_screen_list.get_child(cur)
        self.currentButton.set_style_bg_color(lv.color_make(0xff, 0xff, 0xff), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.set_style_bg_grad_color(lv.color_make(0xff, 0xff, 0xff), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.btn_list[cur][1].set_style_text_color(lv.color_make(0x00, 0x00, 0x00), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.scroll_to_view(lv.ANIM.OFF)

    def clear_state(self, cur=None):
        if cur is None:
            cur = self.cur
        self.currentButton = self.quickkey_screen_list.get_child(cur)
        self.currentButton.set_style_bg_color(lv.color_make(0x00, 0x00, 0x00), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.set_style_bg_grad_color(lv.color_make(0x00, 0x00, 0x00), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.btn_list[cur][1].set_style_text_color(lv.color_make(0xff, 0xff, 0xff), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.scroll_to_view(lv.ANIM.OFF)

    def btn_down_click(self):
        """向下选择"""
        self.clear_state()
        self.cur = self.next_idx(self.cur, len(self.quickkey_menu_list))
        self.add_state()

    def btn_up_click(self):
        """向上选择"""
        self.clear_state()
        self.cur = self.prev_idx(self.cur, len(self.quickkey_menu_list))
        self.add_state()

    def btn_menu_click(self):
        """确认选择按键"""
        meta_info = {"index":self.cur,"number":self.key[self.cur]}
        EventMesh.publish("load_screen",{"screen":"quick_key_option","meta_info":meta_info})

    def btn_back_click(self):
        # 返回按键
        if self.cur > 0:
            self.clear_state()
            self.cur = 0
        if self.flag:
            EventMesh.publish("load_screen", {"screen": "main"})
        else:
            EventMesh.publish("load_screen", {"screen": "setting"})

    def btn_pwk_click(self):
        """呼叫挂断返回通话结束页面"""
        EventMesh.publish("load_screen", {"screen": "main"})
        return True


#升级选择显示界面
class UpdateSelectScreen(Screen):

    NAME = "updateselect"

    def __init__(self):
        super().__init__()
        self.httpsurl = ""
        self.httpurl = ""
        self.call_update_battery = 0
        self.call_update_battery_voltage = 0
        self.meta = update_select_screen
        self.language = "zh"
        self.update_select_screen_label = update_select_screen_label
        self.update_select_screen_sure = update_select_screen_sure
        self.update_select_screen_return = update_select_screen_return

    def initialization(self):
        self.language = EventMesh.publish("persistent_config_get", "language")
        self.httpsurl = EventMesh.publish("persistent_config_get","Url")
        self.httpurl = self.httpsurl.replace("https", "http")
        self.call_update_battery = EventMesh.publish("get_battery_energy")
        self.call_update_battery_voltage = EventMesh.publish("get_battery_voltage")
        if self.language == "zh":
            self.update_select_screen_label.set_text("是否重启更新？")
            self.update_select_screen_sure.set_text("更新")
            self.update_select_screen_return.set_text("取消")
        else:
            self.update_select_screen_label.set_text("Restart to update?")
            self.update_select_screen_sure.set_text("OK")
            self.update_select_screen_return.set_text("Cancel")
        return True

    def btn_menu_click(self):
        """确认选择按键"""
        self.language = EventMesh.publish("persistent_config_get", "language")
        if self.httpurl == None:
            if self.language == "zh":
                EventMesh.publish("window_show", "无升级内容")
            else:
                EventMesh.publish("window_show", "No Upgrades")
        elif self.httpurl[-4:] == ".bin":
            if self.call_update_battery >= 50 and self.call_update_battery_voltage >= 3700:
                if self.language == "zh":
                    EventMesh.publish("window_show", "升级中,请稍等")
                else:
                    EventMesh.publish("window_show", "Upgrading Please Wait...")
                EventMesh.publish("persistent_config_store",{"Url": ""})
                tem = "11"
                EventMesh.publish("persistent_config_store",{"volume_ring": tem})
                EventMesh.publish("persistent_config_store",{"key_tone": tem})
                EventMesh.publish("persistent_config_store",{"volume_call": tem})
                EventMesh.publish("persistent_config_store",{"volume_SMS": tem})
                EventMesh.publish("persistent_config_store",{"call_ring": 1})
                EventMesh.publish("persistent_config_store",{"SMS_ring": 3})
                EventMesh.publish("persistent_config_store",{"Alarm_clock_ring": 2})
                EventMesh.publish("set_audio_volume", 0)
                EventMesh.publish("set_audio_volume", 1)
                EventMesh.publish("set_audio_volume", 2)
                EventMesh.publish("set_audio_volume", 3)
                ret = None 
                regCTCC="{}".format(ret)+","+ ""
                # EventMesh.publish("NV_config_set",["regCTCC",regCTCC])
                EventMesh.publish("pin_disenable_for_update")
                fota_obj = fota()
                if self.httpurl[-14:] == "dfota_file.bin":
                    print("升级包1111:",self.httpurl)
                    res = fota_obj.httpDownload(url1=self.httpurl,callback=self.result)     
            else:
                if self.language == "zh":
                    EventMesh.publish("window_show", "电量低,无法升级")
                else:
                    EventMesh.publish("window_show", "Low Battery And Cannot Be Upgraded")
        else:
            if self.language == "zh":
                EventMesh.publish("window_show", "已是最新版本")
            else:
                EventMesh.publish("window_show", "Already the latest version")
    def btn_back_click(self):
        # 返回按键
        if self.language == "zh":
            EventMesh.publish("window_show", "请在设置中升级")
        else:
            EventMesh.publish("window_show", "Please Upgrade In The Settings")
        EventMesh.publish("load_screen", {"screen": "main"})

    def result(self,args):
        print('download status:',args[0],'download process:',args[1])
        

#升级显示界面
class UpdateScreen(Abstract):

    def __init__(self):
        super().__init__()
        self.httpsurl = ""
        self.httpurl = ""
        self.call_update_battery = 0
        self.call_update_battery_voltage = 0
        self.language = "zh"
    def post_processor_after_instantiation(self):
        EventMesh.subscribe("update_compulsion", self.update_compulsion)

    def update_compulsion(self,topic=None,data=None):
        self.language = EventMesh.publish("persistent_config_get", "language")
        self.httpsurl = EventMesh.publish("persistent_config_get","Url")
        self.call_update_battery = EventMesh.publish("get_battery_energy")
        self.call_update_battery_voltage = EventMesh.publish("get_battery_voltage")
        print("self.httpsurl=",self.httpsurl)
        self.httpurl = self.httpsurl.replace("https", "http")
        if self.httpurl[-4:] == ".bin":
            if self.language == "zh":
                EventMesh.publish("window_show", "升级中,请稍等")
            else:
                EventMesh.publish("window_show", "Upgrading Please Wait...")
            EventMesh.publish("persistent_config_store",{"Url": ""})
            tem = "11"
            EventMesh.publish("persistent_config_store",{"volume_ring": tem})
            EventMesh.publish("persistent_config_store",{"key_tone": tem})
            EventMesh.publish("persistent_config_store",{"volume_call": tem})
            EventMesh.publish("set_audio_volume", 0)
            EventMesh.publish("set_audio_volume", 1)
            EventMesh.publish("set_audio_volume", 2)
            EventMesh.publish("set_audio_volume", 3)
            ret = None 
            regCTCC="{}".format(ret)+","+ ""
            EventMesh.publish("NV_config_set",["regCTCC",regCTCC])
            EventMesh.publish("NV_config_set",["regCMCC",regCTCC])
            EventMesh.publish("pin_disenable_for_update")
            fota_obj = fota()
            if self.httpurl[-14:] == "dfota_file.bin":
                print("升级包22222:",self.httpurl)
                res = fota_obj.httpDownload(url1=self.httpurl,callback=self.result, ssl_params=0)   
        else:
            pass
        
    def result(self,args):
        print('download status:',args[0],'download process:',args[1])

#语言设置菜单--一级页面
class languageSwitchScreen(Screen):
    NAME = "languageSwitch" 
    def __init__(self):
        super().__init__()
        self.meta = language_screen
        self.cur = 0
        self.count = 2
        self.languagesetting_list = language_list
        self.language_list_title = language_list_title
        self.language_list_sure = language_list_sure
        self.language_list_return = language_list_return
        self.btn_list = []
        self.language = "zh"
        self.currentButton = None
        self.languagesetting_menu_list = [
            {
                "title":" 1 简体中文",
            },
            {
                "title":" 2 English",
            },
        ]
    def initialization(self):
        self.btn_list = []
        self.list_create()
        return True
    
    def list_create(self):
        self.languagesetting_list.delete()
        self.languagesetting_list = lv.list(self.meta)
        self.languagesetting_list.set_pos(0, 16)
        self.languagesetting_list.set_size(128, 32)
        self.languagesetting_list.set_style_pad_left(0, 0)
        self.languagesetting_list.set_style_pad_top(0, 1)
        self.languagesetting_list.set_style_pad_row(0, 0)
        self.languagesetting_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
        self.languagesetting_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR)
        self.language = EventMesh.publish("persistent_config_get", "language")
        if self.language == "zh":#中文显示
            self.language_list_title.set_text("语言设置")
            self.language_list_sure.set_text("选择")
            self.language_list_return.set_text("返回")
        else:#英文显示
            self.language_list_title.set_text("Language Setting")
            self.language_list_sure.set_text("Select")
            self.language_list_return.set_text("Back")
        for idx, item in enumerate(self.languagesetting_menu_list):
            language_obj_list_btn = lv.btn(self.languagesetting_list)
            language_obj_list_btn.set_pos(0, 1)
            language_obj_list_btn.set_size(115, 16)
            language_obj_list_btn.add_style(style_list_black, lv.PART.MAIN | lv.STATE.DEFAULT)
            language_obj_list_label = lv.label(language_obj_list_btn)
            language_obj_list_label.center()
            language_obj_list_label.set_pos(0, 2)
            language_obj_list_label.set_size(115, 16)
            language_obj_list_label.set_text(item["title"])
            language_obj_list_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
            self.btn_list.append((language_obj_list_btn, language_obj_list_label))

        self.add_state()
    def add_state(self, cur=None):
        if cur is None:
            cur = self.cur
        self.currentButton = self.languagesetting_list.get_child(cur)
        self.currentButton.set_style_bg_color(lv.color_make(0xff, 0xff, 0xff), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.set_style_bg_grad_color(lv.color_make(0xff, 0xff, 0xff), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.btn_list[cur][1].set_style_text_color(lv.color_make(0x00, 0x00, 0x00), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.scroll_to_view(lv.ANIM.OFF) 

    def clear_state(self, cur=None):
        if cur is None:
            cur = self.cur
        self.currentButton = self.languagesetting_list.get_child(cur)
        self.currentButton.set_style_bg_color(lv.color_make(0x00, 0x00, 0x00), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.set_style_bg_grad_color(lv.color_make(0x00, 0x00, 0x00), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.btn_list[cur][1].set_style_text_color(lv.color_make(0xff, 0xff, 0xff), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.scroll_to_view(lv.ANIM.OFF)    

    def btn_down_click(self):
        """向下选择"""
        self.clear_state()
        self.cur = self.next_idx(self.cur, len(self.languagesetting_menu_list))
        self.add_state()

    def btn_up_click(self):
        """向上选择"""
        self.clear_state()
        self.cur = self.prev_idx(self.cur, len(self.languagesetting_menu_list))
        self.add_state()    

    def btn_menu_click(self):
        """确认选择按键"""
        self.language = EventMesh.publish("persistent_config_get", "language")
        if self.cur == 0:
            self.language = "zh"
            EventMesh.publish("persistent_config_store",{"language": "zh"})  #中文
            EventMesh.publish("window_show", "中文显示")
            print("language:",self.language)
            print("中文")
            utime.sleep_ms(800)
            EventMesh.publish("load_screen", {"screen": "main"})
        if self.cur == 1:
            self.language = "en"
            EventMesh.publish("persistent_config_store",{"language": "en"})  #中文
            EventMesh.publish("window_show", "English Display")
            print("language:",self.language)
            print("英文")
            utime.sleep_ms(800)
            EventMesh.publish("load_screen", {"screen": "main"})
        # tem='%d' %self.cur
        # EventMesh.publish("load_screen", {"screen": self.displaysetting_menu_list[self.cur]["screen"],"meta_info":tem})

    def btn_back_click(self):
        # 返回按键
        if self.cur > 0:
            self.clear_state()
            self.cur = 0
        EventMesh.publish("load_screen", {"screen": "setting"})

    def btn_pwk_click(self):
        """呼叫挂断返回通话结束页面"""
        EventMesh.publish("load_screen", {"screen": "main"})
        return True
################################################################
#设置的一级菜单
################################################################
class  Settingscreen(Screen):
    """
    设置页面
    """
    NAME = "setting"

    def __init__(self):
        super().__init__()
        self.meta = setting_screen
        self.cur = 0
        self.language="zh"
        self.count = 9
        self.setting_screen_list = setting_list
        self.setting_list_title = setting_list_title
        self.setting_list_sure = setting_list_sure
        self.setting_list_return = setting_list_return
        self.btn_list = []
        self.currentButton = None
        self.setting_menu_list = [
            {
                "screen": "soundsetting",
                "title":" 1 声音设置",
            },
            {
                "screen": "displaysetting",
                "title":" 2 显示设置",
            },
            {
                "screen": "Lanport",
                "title": " 3 Lan设置",
            },
            {
                "screen":"hotspotsetting",
                "title": " 4 Hotspot Setting",
            },
            {
                "screen": "securitysetting",
                "title":" 5 安全设置",
            },
            {
                "screen": "networkservice",
                "title":" 6 网络服务",
            },
            {
                "screen": "black_white_setting",
                "title":" 7 黑白名单" 
            },
            {
                "screen": "phoneinfo_select",
                "title":" 8 话机信息",
            },
            {
                "screen": "quickkey",
                "title":" 9 快捷键",
            },          
            {
                "screen": "sipinfo",
                "title":" 10 SIP信息",
            },     
        ]
        self.setting_menu_list_en = [
            {
                "screen": "soundsetting",
                "title":" 1 Sound Setting",
            },
            {
                "screen": "displaysetting",
                "title":" 2 Display Setting",
            },
            {
                "screen": "Lanport",
                "title": " 3 Lan Setting",
            },
            {
                "screen":"hotspotsetting",
                "title": " 4 Hotspot Setting",
            },
            {
                "screen": "securitysetting",
                "title":" 5 Security Setting",
            },
            {
                "screen": "networkservice",
                "title":" 6 Network Service",
            },
            {
                "screen": "black_white_setting",
                "title":" 7 Black Whitelist",
            },
            {
                "screen": "phoneinfo_select",
                "title":" 8 PhoneInfo",
            },
            {
                "screen": "quickkey",
                "title":" 9 QuickKey",
            },
            {
                "screen": "sipinfo",
                "title":" 10 SIPInfo",
            },
        ]

    def initialization(self):
        self.btn_list = []
        self.list_create()
        return True

    def list_create(self):
        self.setting_screen_list.delete()
        self.setting_screen_list = lv.list(self.meta)
        self.setting_screen_list.set_pos(0, 16)
        self.setting_screen_list.set_size(128, 32)
        self.setting_screen_list.set_style_pad_left(0, 0)
        self.setting_screen_list.set_style_pad_top(0, 1)
        self.setting_screen_list.set_style_pad_row(0, 0)
        self.setting_screen_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
        self.setting_screen_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR)
        self.language = EventMesh.publish("persistent_config_get", "language")
        if self.language == "zh":
            self.option=self.setting_menu_list
            self.setting_list_title.set_text("话机设置")
            self.setting_list_sure.set_text("选择")
            self.setting_list_return.set_text("返回")
        else:
            self.option=self.setting_menu_list_en
            self.setting_list_title.set_text("Phone Settings")
            self.setting_list_sure.set_text("Select")
            self.setting_list_sure.set_size(48, 12)
            self.setting_list_return.set_pos(104, 51)
            self.setting_list_return.set_text("Back")
        for idx, item in enumerate(self.option):
            setting_obj_list_btn = lv.btn(self.setting_screen_list)
            setting_obj_list_btn.set_pos(0, 1)
            setting_obj_list_btn.set_size(115, 16)
            setting_obj_list_btn.add_style(style_list_black, lv.PART.MAIN | lv.STATE.DEFAULT)
            setting_obj_list_label = lv.label(setting_obj_list_btn)
            setting_obj_list_label.center()
            setting_obj_list_label.set_pos(0, 2)
            setting_obj_list_label.set_size(115, 16)
            setting_obj_list_label.set_text(item["title"])
            setting_obj_list_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
            self.btn_list.append((setting_obj_list_btn, setting_obj_list_label))

        self.add_state()

    def add_state(self, cur=None):
        if cur is None:
            cur = self.cur
        self.currentButton = self.setting_screen_list.get_child(cur)
        self.currentButton.set_style_bg_color(lv.color_make(0xff, 0xff, 0xff), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.set_style_bg_grad_color(lv.color_make(0xff, 0xff, 0xff), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.btn_list[cur][1].set_style_text_color(lv.color_make(0x00, 0x00, 0x00), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.scroll_to_view(lv.ANIM.OFF)

    def clear_state(self, cur=None):
        if cur is None:
            cur = self.cur
        self.currentButton = self.setting_screen_list.get_child(cur)
        self.currentButton.set_style_bg_color(lv.color_make(0x00, 0x00, 0x00), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.set_style_bg_grad_color(lv.color_make(0x00, 0x00, 0x00), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.btn_list[cur][1].set_style_text_color(lv.color_make(0xff, 0xff, 0xff), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.scroll_to_view(lv.ANIM.OFF)

    def btn_down_click(self):
        """向下选择"""
        self.clear_state()
        self.cur = self.next_idx(self.cur, len(self.setting_menu_list))
        self.add_state()

    def btn_up_click(self):
        """向上选择"""
        self.clear_state()
        self.cur = self.prev_idx(self.cur, len(self.setting_menu_list))
        self.add_state()

    def btn_menu_click(self):
        """确认选择按键"""
        self.language = EventMesh.publish("persistent_config_get", "language")
        if self.cur == 9:
            EventMesh.publish("load_screen", {"screen": "sipinfo"})
        elif self.cur == 8:
            EventMesh.publish("load_screen", {"screen": "quickkey","meta_info":{"flag":"0"}})
        else:
            # screen = 7
            # if self.count == 11:
            #     screen = 8
            if self.cur == 3 or self.cur == 5:
                status = sim.getStatus()
                if status == 0:
                    if self.language == "zh":#中文显示
                        EventMesh.publish("window_show", "未插SIM卡")
                    else:#英文显示
                        EventMesh.publish("window_show", "No Insert SIM Card")
                    return
            if self.cur == 2: # Lan设置
                lan_switch = EventMesh.publish("persistent_config_get","eth_status")
                print("Lan设置",lan_switch)
                EventMesh.publish("load_screen", {"screen": self.setting_menu_list[self.cur]["screen"],"meta_info": lan_switch})
                return
            EventMesh.publish("load_screen", {"screen": self.setting_menu_list[self.cur]["screen"]})

    def btn_back_click(self):
        # 返回按键
        if self.cur > 0:
            self.clear_state()
            self.cur = 0
        EventMesh.publish("load_screen", {"screen": "menu"})

    def btn_pwk_click(self):
        """呼叫挂断返回通话结束页面"""
        EventMesh.publish("load_screen", {"screen": "main"})
        return True
    
class LanPortScreen(Screen):
    NAME = "Lanport"

    def __init__(self):
        super().__init__()
        self.meta = Lan_setting_screen
        self.Lan_setting_list=Lan_setting_list
        self.Lan_setting_list_title=Lan_setting_list_title
        self.Lan_setting_list_sure=Lan_setting_list_sure
        self.Lan_setting_list_return=Lan_setting_list_return

        self.cur = 0
        self.btn_list = []

    def initialization(self):
        self.lanswitch = self.meta_info
        print("self.lanswitch::",self.lanswitch)
        self.lanport_menu_list_en = [
            "lan_Switch",
            "Details",
        ]
        self.Lan_setting_list_title.set_text("Lan Setting")
        self.Lan_setting_list_sure.set_text("Select")
        Lan_setting_list_return.set_text("Back")
        self.btn_list = []
        self.list_create()

    def list_create(self):
        self.Lan_setting_list.delete()
        self.Lan_setting_list = lv.list(self.meta)
        self.Lan_setting_list.set_pos(0, 16)
        self.Lan_setting_list.set_size(128, 32)
        self.Lan_setting_list.set_style_pad_left(0, 0)
        self.Lan_setting_list.set_style_pad_top(0, 1)
        self.Lan_setting_list.set_style_pad_row(0, 0)
        self.Lan_setting_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
        self.Lan_setting_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR)

        self.option=self.lanport_menu_list_en
        for idx, item in enumerate(self.option):
            lanport_obj_list_btn = lv.btn(self.Lan_setting_list)
            lanport_obj_list_btn.set_pos(0, 1)
            lanport_obj_list_btn.set_size(115, 16)
            lanport_obj_list_btn.add_style(style_list_black, lv.PART.MAIN | lv.STATE.DEFAULT)
            lanport_obj_list_label = lv.label(lanport_obj_list_btn)
            lanport_obj_list_label.center()
            lanport_obj_list_label.set_pos(0, 2)
            lanport_obj_list_label.set_size(115, 16)
            lanport_obj_list_label.set_text(" {} {}".format(str(idx + 1), item))
            lanport_obj_list_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
            self.btn_list.append((lanport_obj_list_btn, lanport_obj_list_label))

        self.add_state()

    def add_state(self, cur=None):
        if cur is None:
            cur = self.cur
        self.currentButton = self.Lan_setting_list.get_child(cur)
        self.currentButton.set_style_bg_color(lv.color_make(0xff, 0xff, 0xff), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.set_style_bg_grad_color(lv.color_make(0xff, 0xff, 0xff), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.btn_list[cur][1].set_style_text_color(lv.color_make(0x00, 0x00, 0x00), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.scroll_to_view(lv.ANIM.OFF)

    def clear_state(self, cur=None):
        if cur is None:
            cur = self.cur
        self.currentButton = self.Lan_setting_list.get_child(cur)
        self.currentButton.set_style_bg_color(lv.color_make(0x00, 0x00, 0x00), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.set_style_bg_grad_color(lv.color_make(0x00, 0x00, 0x00), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.btn_list[cur][1].set_style_text_color(lv.color_make(0xff, 0xff, 0xff), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.scroll_to_view(lv.ANIM.OFF)

    def btn_down_click(self):
        """向下选择"""
        self.clear_state()
        self.cur = self.next_idx(self.cur, len(self.lanport_menu_list_en))
        self.add_state()

    def btn_up_click(self):
        """向上选择"""
        self.clear_state()
        self.cur = self.prev_idx(self.cur, len(self.lanport_menu_list_en))
        self.add_state()

    def btn_menu_click(self):
        """确认选择按键"""
        if self.cur == 0:
            EventMesh.publish("load_screen", {"screen": "lan_switch"})
        if self.cur == 1:
            EventMesh.publish("load_screen", {"screen": "Lan_Details"})

    def btn_back_click(self):
        # 返回按键
        if self.cur > 0:
            self.clear_state()
            self.cur = 0
        EventMesh.publish("load_screen", {"screen": "setting"})

    def btn_pwk_click(self):
        EventMesh.publish("load_screen", {"screen": "main"})


class DisplayCharging(object):
    instance = None

    def __init__(self):
        self.get_charge_icon_timer = osTimer()
        self.next_charging_idx = 0
        self.charge_timer_running = 0
        EventMesh.subscribe("display_charge_icon_start", self.start_timer)

    def get_charge_icon_handle(self,args):
        if self.charge_timer_running == 1:
            self.get_charge_icon_timer.stop()
            self.charge_timer_running = 0
        if EventMesh.publish("get_current_screen_name") == "main":
            if EventMesh.publish("get_charging_state") == 1:
                if EventMesh.publish("get_tempture_valid") == 0:
                    if EventMesh.publish("get_charging_full_state") == 0:
                        self.next_charging_idx += 1
                        if self.next_charging_idx >= 5:
                            self.next_charging_idx = 0
                        EventMesh.publish("update_screen_battery","U:/static/charge_battery_{}.png".format(str(self.next_charging_idx)))
                        self.charge_timer_running = 1
                        self.get_charge_icon_timer.start(1000,1,self.get_charge_icon_handle)
                        return
            EventMesh.publish("update_screen_battery",EventMesh.publish("get_battery_img"))
    def start_timer(self, event, state):
        if self.charge_timer_running == 0:
            self.charge_timer_running = 1
            self.get_charge_icon_timer.start(1000,1,self.get_charge_icon_handle)
        else:
            self.get_charge_icon_timer.stop()
            self.get_charge_icon_timer.start(1000,1,self.get_charge_icon_handle)

    @classmethod
    def build(cls):
        if not cls.instance:
            cls.instance = cls()
        return cls.instance

class MainScreen(Screen):
    """主页面 待机页面"""
    NAME = "main"

    def __init__(self):
        super().__init__()
        self.meta = main_screen
        self.main_menu_label = main_menu_label
        self.main_contacts_label = main_contacts_label
        #self.main_volte_img = main_volte_img 
        self.main_wifi_img = main_wifi_img 
        self.main_data_img = main_data_img
        self.main_sign_img = main_sign_img
        self.main_incall_img = main_incall_img
        self.main_sms_img = main_sms_img
        self.main_sip_img = main_sip_img
        self.main_battery_img = main_battery_img
        self.main_alarm_img = main_alarm_img
        self.main_net_connect_img=main_net_connect_img
        self.main_operator_label = main_operator_label
        self.main_date_time_label = main_date_time_label
        self.main_disturb_label =main_disturb_label
        self.display_charging = DisplayCharging()
        self.main_dialed_call = 0
        self.no_sim_state = 0
        self.sip_img_flag = 0
        self.language="zh"
        self.display_charging_content = [
            "U:/static/charge_battery_0.png",
            "U:/static/charge_battery_1.png",
            "U:/static/charge_battery_2.png",
            "U:/static/charge_battery_3.png",
            "U:/static/charge_battery_4.png"
        ]
        self.charging_full_debounce = 0
        self.operator_list = [
              "中国电信",
              "中国移动",
              "中国联通",
              "中国广电"
        ]
        self.disturb=0 #免打扰标志 0 关免打扰 1开免打扰
    def post_processor_after_instantiation(self):
        """注册后台调用事件"""
        EventMesh.subscribe("signal", self.update_signal_info)
        EventMesh.subscribe("update_screen_sms", self.display_message_icon)
        EventMesh.subscribe("update_screen_miss", self.display_misscall_icon)
        EventMesh.subscribe("update_screen_wifi", self.display_wifi_icon)
        EventMesh.subscribe("update_screen_time", self.update_date_time_info)
        EventMesh.subscribe("update_screen_battery", self.update_battery_info)
        EventMesh.subscribe("display_operator_info",self.update_operator_info)
        EventMesh.subscribe("display_of_network_cable_icons", self.display_of_network_cable_icons)
        EventMesh.subscribe("update_sip_img",self.update_sip_img)
        EventMesh.subscribe("display_sip_operator_info",self.update_sip_operator_info)
        
    def initialization(self):
        self.language = EventMesh.publish("persistent_config_get", "language")
        """初始化页面属性"""
        self.language_change()
        # self.no_sim_state = 0
        self.update_sip_operator_tip()
        #self.update_operator_info(operator=EventMesh.publish("get_device_ope"))
        signal = EventMesh.publish("screen_get_sig")
        if signal:
            self.update_signal_info(signal=signal)
        self.display_sip_img()
        self.display_message_icon()
        self.display_misscall_icon()
        if EventMesh.publish("persistent_config_get","wifi_value_flag")==1 and dataCall.getInfo(1,0)[2][2]!="0.0.0.0":
            EventMesh.publish("update_screen_wifi", 1)
        else:
            EventMesh.publish("update_screen_wifi", 0)
        # 回到主界面开始显示充电状态 EventMesh.publish("get_battery_img")这个接口里有启动充电显示的定时器
        self.update_battery_info(battery=EventMesh.publish("get_battery_img"))
        EventMesh.publish("display_charge_icon_start")
        date_time = self.publish_time()
        if date_time:
            self.update_date_time_info(date_time=date_time)
        else:
            self.update_date_time_info()
        #待机界面关掉声音
        EventMesh.publish("audio_tone_stop")
        EventMesh.publish("reset_input_number")
        # self.update_disturb()
        print("进入主界面")
        return True

    def language_change(self):
        """英文界面"""
        if self.language == "en":
            self.main_menu_label.set_text("Menu")
            self.main_contacts_label.set_text("Contacts")
            self.main_contacts_label.set_pos(77, 51)
            self.main_contacts_label.set_size(50, 12)
        else:
            self.main_menu_label.set_text("菜单")
            self.main_contacts_label.set_text("电话本")
            self.main_contacts_label.set_pos(90, 51)
            self.main_contacts_label.set_size(36, 12)

    def get_input_method(self):
        return "123"

    def btn_symbol_click(self, number):
        print("MainScreen:btn_symbol_click",number)
        self.btn_num_click(number)

    def display_of_network_cable_icons(self, topic=None, state=None):
        """显示网络电缆图标"""
        if state == 1:
            main_Ethernet_img.set_size(12, 12)
        else:
            main_Ethernet_img.set_size(0, 0)

    def btn_num_long_click(self,number):
        """进入呼叫页面"""
        key = EventMesh.publish("persistent_config_get", "speed_dial_number")
        if number == "*" or number == "#":
            return
        index = int(number)-2
        if index < 0:
            return
        if key == None:
            key = ["","","","","","","",""]
        if key[index] == "":
            EventMesh.publish("load_screen", {"screen":"speed_dial","meta_info":{"flag":"1"}})
            return
        status = sim.getStatus()
        if status == 0:
            if not (key[index]=="110" or key[index]=="112" or key[index] =="119" or key[index] =="120" or key[index] =="122" or key[index] == "911"):
                if self.language == "zh":
                    EventMesh.publish("window_show", "仅能拨打紧急号码")
                else:
                    EventMesh.publish("window_show", "Only Emergency Numbers Can Be Dialed")
                return                
        else:
            EventMesh.publish("load_screen", {"screen": "call_out", "meta_info": ["",key[index]]})

    def btn_fast_long_click(self,No):
        """进入快捷键编辑页面"""
        print("No_fast=:",No)

    def btn_menu_click(self):
        """按下跳转菜单页面"""
        EventMesh.publish("load_screen", {"screen": "menu"})

    def btn_back_click(self):
        """按下跳转联系人页面"""
        EventMesh.publish("load_screen", {"screen": "phone_book","meta_info":"0"})

    def btn_num_click(self, number):
        """进入拨号页面"""
        EventMesh.publish("load_screen", {"screen": "keypad_input", "meta_info": ["8",number]})
    
    def btn_call_click(self):
        """进入已拨电话页面"""
        call_logs_list = EventMesh.publish("persistent_config_get", "call_logs_list")
        self.dialed_call_menu_list = [sublist for sublist in call_logs_list if sublist[0] == 2]
        self.count = len(self.dialed_call_menu_list)
        print("已拨电话数量：",self.count)
        self.main_dialed_call = 0
        if self.count == 0:
            self.language = EventMesh.publish("persistent_config_get", "language")
            if self.language == "zh":
                EventMesh.publish("window_show", "无已拨电话")
            else:
                EventMesh.publish("window_show", "No dialed calls")
        else:
            EventMesh.publish("load_screen", {"screen": "dialed_call"})
            self.main_dialed_call = 1
        EventMesh.publish("persistent_config_store", {"main_dialed_call":self.main_dialed_call})

    def update_operator_info(self, topic=None, operator=None):
        """更新运营商信息"""
        self.language = EventMesh.publish("persistent_config_get", "language")
        if self.language == "en":
            operator = operator.replace("中国电信","China Telecom")
            operator = operator.replace("中国移动","China Mobile")
            operator = operator.replace("中国联通","China Unicom")
            operator = operator.replace("中国广电","China Broadcasting")
            operator = operator.replace("未插卡","No SIM card")
            operator = operator.replace("SIM卡暂时未识别","SIM card unrecognized")
            operator = operator.replace("未知运营商","Unknown operator")

        locked_operator = EventMesh.publish("NV_config_get","lockOperator")
        locked_operator =None
        if locked_operator == None:
            locked_operator = "4"
        int_lock = int(locked_operator)
        if int_lock != 4 :              #有锁定的运营商 0-3
            if self.operator_list[int_lock] != operator and operator != "未插卡" and operator != "SIM卡未识别":
                self.no_sim_state = 1
                self.main_operator_label.set_text("请插入{}卡".format(self.operator_list[int_lock]))
            elif self.operator_list[int_lock] == operator:
                self.main_operator_label.set_text(operator)
            elif operator == "未插卡" or operator == "No SIM card":
                self.main_operator_label.set_text(operator)
            elif operator == "SIM卡暂时未识别":
                self.main_operator_label.set_text(operator)
        else:                           #无锁定运行商 4
            if operator is None:
                if self.language == "zh":
                    print("weichaka")
                    operator = "未插卡"
                else:
                    operator = "No SIM card"
                self.main_operator_label.set_text(operator)
            else:
                self.main_operator_label.set_text(operator)
            # print("operator:",operator)
        if operator == "未插卡" or operator == "No SIM card":
            self.no_sim_state = 1
        else:
            self.no_sim_state = 0

    def update_disturb(self):
        disturb=EventMesh.publish("persistent_config_get","disturb")
        if disturb=="0" or disturb==None:
            self.main_disturb_label.set_text("")
        else:
            self.main_disturb_label.set_text("do not disturb")

    def update_signal_info(self, topic=None, signal=31):
        """更新信号强度信息"""
        select_value = EventMesh.publish("persistent_config_get", "dataSelect")
        if select_value==None or int(select_value[0])==0:
            tem = 1
        else:
            tem = 0
        #获取sim卡注册状态
        resp = bytearray(64)        
        if atcmd.sendSync('AT+CIREG?IMS\r\n',resp,'',20) == 0:
            string_simreg_state = resp.decode('utf-8')
            simreg_state = int(string_simreg_state[10])
        else:
            simreg_state = 0
        if not self.no_sim_state:
            if 5 < signal <= 31:
                if signal > 25:
                    signal = 5
                elif signal > 20:
                    signal = 4
                elif signal > 15:
                    signal = 3
                elif signal > 10:
                    signal = 2
                else:
                    signal = 1     
            if simreg_state :
                if tem == 1:
                    self.main_data_img.set_pos(0, 2)
                    self.main_sign_img.set_pos(7, 2)
                   #self.main_volte_img.set_size(10,12)
                    self.main_data_img.set_size(7, 12)
                    self.main_sign_img.set_size(15,12)
                   #self.main_volte_img.set_src("U:/static/volte.png")
                else:
                    self.main_sign_img.set_pos(0, 2)
                    self.main_data_img.set_size(0, 0)
                    #self.main_volte_img.set_size(10,12)
                    self.main_sign_img.set_size(15,12)
                self.main_sign_img.set_src('U:/static/signal_'+str(signal)+'.png')
            else:           #物联网卡
                if signal == 99:
                    signal = 4
                self.main_data_img.set_pos(0, 2)
                self.main_sign_img.set_pos(7, 2)
                #self.main_volte_img.set_size(0,0)
                self.main_data_img.set_size(7, 12)
                self.main_sign_img.set_size(15,12)
                self.main_sign_img.set_src('U:/static/signal_'+str(signal)+'.png')
        else:
            #self.main_volte_img.set_size(0,0)
            self.main_data_img.set_size(0,0)
            self.main_sign_img.set_size(0,0) 

    def update_miss_icon(self, topic=None, miss_call=0):
        if miss_call == 0:
            main_incall_img.set_size(0, 0)
        else:
            main_incall_img.set_size(12, 12)

    def update_sms_icon(self, topic=None, display=0):
        if display == 0:
            main_sms_img.set_size(0, 0)
        else:
            main_sms_img.set_size(12, 12)
            main_sms_img.set_src("U:/static/message_n.png")

    def display_message_icon(self,topic=None, event=0):
        message_id = EventMesh.publish("persistent_config_get","messageid")
        if message_id == None:
            self.update_sms_icon(display=0)
        else:
            for idx,message_index in enumerate(message_id):
                if message_index["read"] == "0":
                    self.update_sms_icon(display=1)
                    return
            self.update_sms_icon(display=0)

    def display_misscall_icon(self,topic=None, event=0):
        call_logs_list = EventMesh.publish("persistent_config_get", "call_logs_list")
        if call_logs_list == None:
            self.update_miss_icon(miss_call=0)
        else:
            for idx,call_log in enumerate(call_logs_list):
                if call_log[0] == 0:
                    self.update_miss_icon(miss_call=1)
                    return
            self.update_miss_icon(miss_call=0)

    def display_wifi_icon(self,topic=None, data=0):
        if data == 0:
            self.update_wifi_icon(msg=0)
        else:
            self.update_wifi_icon(msg=1)

    def update_wifi_icon(self, topic=None, msg=0):
        if msg == 0:
            main_wifi_img.set_size(0, 0)
        else:
            main_wifi_img.set_size(12, 12)
            main_wifi_img.set_src("U:/static/wifi.png")

    def update_battery_info(self, topic=None, battery=None):
        if battery is None:
            battery = "U:/static/charge_battery_4.png"
        self.main_battery_img.set_src(battery)

    def update_date_time_info(self, topic=None, date_time=None):
        """更新日期时间显示"""
        if date_time is None:
            date_time = "2023-00-00 00:00"
        self.main_date_time_label.set_text(date_time)
        self.update_alarm()
        self.update_net()
    def update_alarm(self, topic=None, date_time=None):
        if EventMesh.publish("persistent_config_get","alarm_flag") in ["0",None]:
            # print("无闹钟")
            self.main_alarm_img.set_size(0, 0)
        else:
            # print("有闹钟")
            alarm_url = "U:/static/alarm.png"
            self.main_alarm_img.set_size(12, 12)
            self.main_alarm_img.set_src(alarm_url)
            
    def display_sip_img(self):
        """显示SIP图标"""
        if self.sip_img_flag == 0:
            self.main_sip_img.set_size(0, 0)
        else:
            self.main_sip_img.set_size(12, 12)
            self.main_sip_img.set_src("U:/static/sip.png")

    def update_sip_img(self, topic=None, msg=None):
        """更新SIP图标"""
        self.sip_img_flag = msg
        print("update_sip_img=",self.sip_img_flag)
        self.display_sip_img()
            
    def update_sip_operator_info(self, topic=None, operator=None):
        """更新SIP信息"""
        self.main_operator_label.set_text(operator)
    
    def update_sip_operator_tip(self, topic=None, operator=None):
        if EventMesh.publish("get_sip_registered") == 0:
            operator = "SIP Not Registered"
        elif EventMesh.publish("get_sip_registered") == 1:
            operator = "SIP Registered"
        elif EventMesh.publish("get_sip_registered") == 2:
            operator = "Registering SIP"
        else:
            operator = "SIP Not Registered"
        print("update_sip_operator_tip ",operator)
        self.main_operator_label.set_text(operator)
            
    def update_net(self, topic=None, date_time=None):
        if EventMesh.publish("sim_card_net_status")==1:
            self.main_net_connect_img.set_size(12, 12)
            self.main_net_connect_img.set_src("U:/static/connect_CMCC.png")
        else:
            self.main_net_connect_img.set_size(12, 12)
            self.main_net_connect_img.set_src("U:/static/disconnect_CMCC.png")
    def btn_headset_click(self):
        if self.no_sim_state == 1:
            print("无SIM卡")
            return
        else:
            if EventMesh.publish("persistent_config_get","wifi_value_flag")==0 or EventMesh.publish("persistent_config_get","wifi_value_flag")==None:
                EventMesh.publish("wifi_OPEN")
                print("开wifi")  
            elif EventMesh.publish("persistent_config_get","wifi_value_flag")==1:
                EventMesh.publish("wifi_close")
                print("关wifi.....")
                
    def btn_redial_click(self):
        #重拨按键
        call_logs_list = EventMesh.publish("persistent_config_get", "call_logs_list")
        self.dialed_call_menu_list = [sublist for sublist in call_logs_list if sublist[0] == 2]
        self.count = len(self.dialed_call_menu_list)
        self.language = EventMesh.publish("persistent_config_get", "language")
        if self.count == 0:
            if self.language == "zh":
                EventMesh.publish("window_show", "已拨记录为空")
            else:
                EventMesh.publish("window_show", "The Dialed Record Is Empty")
        else:
            status = sim.getStatus()
            if status == 0:
                if not (self.dialed_call_menu_list[0][2]=="110" or self.dialed_call_menu_list[0][2]=="112" or self.dialed_call_menu_list[0][2]=="119" or self.dialed_call_menu_list[0][2]=="120" or self.dialed_call_menu_list[0][2]=="122" or self.dialed_call_menu_list[0][2] == "911"):
                    if self.language == "zh":
                        EventMesh.publish("window_show", "仅能拨打紧急号码")
                    else:
                        EventMesh.publish("window_show", "Only Emergency Numbers Can Be Dialed")
                    return  
            EventMesh.publish("load_screen", {"screen": "call_out", "meta_info": [self.dialed_call_menu_list[0][1], self.dialed_call_menu_list[0][2]]})
            
    def btn_pwk_click(self):
        return True
    
    # def btn_left_click(self):
    #     if self.no_sim_state == 1:
    #         print("无SIM卡")
    #         return
    #     else:
    #         print("关免打扰")
    #         self.disturb=0

    # def btn_right_click(self):
    #     if self.no_sim_state == 1:
    #         print("无SIM卡")
    #         return
    #     else:
    #         print("开免打扰")
    #         self.disturb=1

class MenuScreen(Screen):
    """菜单页面"""
    NAME = "menu"

    def __init__(self):
        super().__init__()
        self.meta = menu
        self.cur = 0
        self.menu_img = menu_img
        self.menu_select_label = menu_select_label
        self.menu_back_label = menu_back_label
        self.menu_title_label = menu_title_label
        self.menu_pro = [
            {
                "title": "Message",
                "screen": "MessageScreen",
                "url": "U:/static/menu_message.png",
            },
            {
                "title": "Call Logs",
                "screen": "calls",
                "url": "U:/static/menu_call.png",
            },
            {
                "title": "Phonebook",
                "screen": "phone_book",
                "url": "U:/static/menu_contact.png",
            },
            # {
            #     "title": "App",
            #     "screen": "application",
            #     "url": "U:/static/app.png",
            # },
            {
                "title": "Settings",
                "screen": "setting",
                "url": "U:/static/menu_settings.png",
            }
        ]

    def initialization(self):
        self.language = EventMesh.publish("persistent_config_get","language")
        if self.meta_info.get("init") and self.cur > 0:
            self.clear_state()
            self.cur = 0
        self.language_change()
        self.add_state()
        return True
    
    def language_change(self):
        """英文界面"""
        if self.language == "en":
            self.menu_pro[0]["title"] = "Message"
            self.menu_pro[1]["title"] = "Call Logs"
            self.menu_pro[2]["title"] = "Contacts"
            # self.menu_pro[3]["title"] = "applications"
            self.menu_pro[3]["title"] = "Settings"
            self.menu_select_label.set_text("Select")
            self.menu_select_label.set_size(40, 12)
            self.menu_back_label.set_text("Back")
        else:
            self.menu_pro[0]["title"] = "短信息"
            self.menu_pro[1]["title"] = "通话记录"
            self.menu_pro[2]["title"] = "电话本"
            # self.menu_pro[3]["title"] = "应用"
            self.menu_pro[3]["title"] = "设置"
            self.menu_select_label.set_text("选择")
            self.menu_back_label.set_text("返回")

    def add_state(self, cur=None):
        """页面加载"""
        if not cur:
            cur = self.cur
        self.menu_img.set_src(self.menu_pro[cur]["url"])
        self.menu_title_label.set_text(self.menu_pro[cur]["title"])

    def clear_state(self, cur=None):
        """页面切换"""
        if not cur:
            cur = self.cur
        self.menu_img.set_src(self.menu_pro[cur]["url"])
        self.menu_title_label.set_text(self.menu_pro[cur]["title"])

    def btn_menu_click(self):
        """确认选择按键"""
        menu_screen = self.menu_pro[self.cur]["screen"]
        if self.cur == 0:
            meta_info = {"flag_back": "0"}
            EventMesh.publish("load_screen", {"screen": menu_screen, "meta_info": meta_info})
        elif self.cur == 2:
            EventMesh.publish("load_screen", {"screen": menu_screen,"meta_info":"1"})
        else:
            EventMesh.publish("load_screen", {"screen": menu_screen})

    def btn_back_click(self):
        """返回菜单页面"""
        EventMesh.publish("load_screen", {"screen": "main"})

    def btn_down_click(self):
        """向下选择"""
        self.btn_right_click()

    def btn_right_click(self):
        """向右选择"""
        self.clear_state()
        self.cur = self.next_idx(self.cur, len(self.menu_pro))
        self.add_state()

    def btn_up_click(self):
        """向上选择"""
        self.btn_left_click()

    def btn_left_click(self):
        """向左选择"""
        self.clear_state()
        self.cur = self.prev_idx(self.cur, len(self.menu_pro))
        self.add_state()

class PhoneBookScreen(Screen):
    """
    电话本页面
    """
    NAME = "phone_book"

    def __init__(self):
        super().__init__()
        self.meta = phone_book
        self.cur = 0
        self.count = 4
        self.language="zh"
        self.phone_book_title = phone_book_title
        self.phone_book_list = phone_book_list
        self.phone_book_select_label = phone_book_select_label
        self.phone_book_back_label = phone_book_back_label
        self.btn_list = []
        self.currentButton = None
        self.phone_book_menu_list = [
            " 1 Phonebook All",
            " 2 Create Phonebook",
            " 3 Delete All",
			" 4 Fast Dial"
        ]
        self.phone_book_menu_list_en = [
            " 1 Phonebook All",
            " 2 Create Phonebook",
            " 3 Delete All",
			" 4 Fast Dial"
        ]

    def initialization(self):
        self.btn_list = []
        self.list_create()
        return True

    def list_create(self):
        self.phone_book_list.delete()
        self.phone_book_list = lv.list(self.meta)
        self.phone_book_list.set_pos(0, 16)
        self.phone_book_list.set_size(128, 32)
        self.phone_book_list.set_style_pad_left(0, 0)
        self.phone_book_list.set_style_pad_top(0, 1)
        self.phone_book_list.set_style_pad_row(0, 0)
        self.phone_book_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
        self.phone_book_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR)
        self.phone_book_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.SCROLLED)
        self.language = EventMesh.publish("persistent_config_get", "language")
        self.language = "en"
        if self.language == "zh":
            self.option=self.phone_book_menu_list
            self.phone_book_title.set_text("电话本")
            self.phone_book_select_label.set_text("选择")
            self.phone_book_back_label.set_text("返回")
        else:
            self.option=self.phone_book_menu_list_en
            self.phone_book_title.set_text("PhoneBook")
            self.phone_book_select_label.set_text("Select")
            self.phone_book_select_label.set_size(48, 12)
            self.phone_book_back_label.set_text("Back")
        for idx, item in enumerate(self.option):
            phone_book_list_btn = lv.btn(self.phone_book_list)
            phone_book_list_btn.set_pos(0, 1)
            phone_book_list_btn.set_size(115, 16)
            phone_book_list_btn.add_style(style_list_black, lv.PART.MAIN | lv.STATE.DEFAULT)
            phone_book_list_label = lv.label(phone_book_list_btn)
            phone_book_list_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
            phone_book_list_label.set_pos(1, 2)
            phone_book_list_label.set_size(115, 16)
            phone_book_list_label.set_text(item)
            self.btn_list.append((phone_book_list_btn, phone_book_list_label))
        
        self.add_state()

    def add_state(self, cur=None):
        if cur is None:
            cur = self.cur
        self.currentButton = self.phone_book_list.get_child(cur)
        self.currentButton.set_style_bg_color(lv.color_make(0xff, 0xff, 0xff), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.set_style_bg_grad_color(lv.color_make(0xff, 0xff, 0xff), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.btn_list[cur][1].set_style_text_color(lv.color_make(0x00, 0x00, 0x00), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.scroll_to_view(lv.ANIM.OFF)

    def clear_state(self, cur=None):
        if cur is None:
            cur = self.cur
        self.currentButton = self.phone_book_list.get_child(cur)
        self.currentButton.set_style_bg_color(lv.color_make(0x00, 0x00, 0x00), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.set_style_bg_grad_color(lv.color_make(0x00, 0x00, 0x00), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.btn_list[cur][1].set_style_text_color(lv.color_make(0xff, 0xff, 0xff), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.scroll_to_view(lv.ANIM.OFF)

    def btn_menu_click(self):
        # 选择按键
        self.language = EventMesh.publish("persistent_config_get", "language")
        if self.cur == 0:
            EventMesh.publish("load_screen", {"screen": "phone_book_select"})
        elif self.cur == 1:
            # 编辑电话本 prev_screen = 4表示编辑电话本界面进入
            meta_info = {"msg":"","number":"","prev_screen":"4"}
            EventMesh.publish("load_screen", {"screen": "phone_book_store_select","meta_info":meta_info})
        elif self.cur == 2:
            # 删除全部
            meta_info = {"prev_screen": "17"}
            EventMesh.publish("load_screen", {"screen": "phone_book_com_select","meta_info":meta_info})
        elif self.cur == 3:
            #快捷拨号
            EventMesh.publish("load_screen", {"screen": "speed_dial","meta_info":{"flag":"0"}})

    def btn_back_click(self):
        # 返回按键
        if self.cur > 0:
            self.clear_state()
            self.cur = 0
        if self.meta_info == "1":
            EventMesh.publish("load_screen", {"screen": "menu"})
        else:
            EventMesh.publish("load_screen", {"screen": "main"})

    def btn_up_click(self):
        self.clear_state()
        self.cur = self.prev_idx(self.cur, self.count)
        self.add_state()

    def btn_down_click(self):
        self.clear_state()
        self.cur = self.next_idx(self.cur, self.count)
        self.add_state()

class PhoneBookComSelect(Screen):
    """
    通讯录电话本查看界面
    话机和sim卡
    """
    NAME = "phone_book_com_select"
    
    def __init__(self):
        super().__init__()
        self.meta = phone_book_com_select
        self.cur = 0
        self.count =2
        self.prev_screen_bak = 0
        self.phone_book_com_select_title = phone_book_com_select_title
        self.phone_book_com_select_list = phone_book_com_select_list
        self.phone_book_com_select_sure_label = phone_book_com_select_sure_label
        self.phone_book_com_select_back_label = phone_book_com_select_back_label
        self.btn_list = []
        self.currentButton = None
        self.name = ""
        self.language = "en"
        self.phone_book_com_select_menu_list = [
            " 1 话机",
            " 2 SIM卡"
        ]
        self.phone_book_com_select_menu_list_en = [
            " 1 Telephone",
            " 2 SIM Card"
            
        ]
        
    def initialization(self):
        self.cur = 0
        self.btn_list = []
        if self.meta_info != None:
            self.prev_screen = int(self.meta_info.get("prev_screen"))
            if self.prev_screen == 16:
                self.prev_screen_bak = self.meta_info.get("prev_screen_back")
            if self.meta_info.get("name"):
                self.name = self.meta_info.get("name")
            print("SendMessageOptionScreen.initialization.meta_info:",self.meta_info," prev_screen:",self.prev_screen)
        self.list_create()
        return True
    
    def list_create(self):
        self.phone_book_com_select_list.delete()
        self.phone_book_com_select_list = lv.list(self.meta)
        self.phone_book_com_select_list.set_pos(0, 16)
        self.phone_book_com_select_list.set_size(128, 32)
        self.phone_book_com_select_list.set_style_pad_left(0, 0)
        self.phone_book_com_select_list.set_style_pad_top(0, 1)
        self.phone_book_com_select_list.set_style_pad_row(0, 0)
        self.phone_book_com_select_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
        self.phone_book_com_select_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR)
        # self.language = EventMesh.publish("persistent_config_get", "language")
        self.language == "en"
        if self.language == "zh":#中文显示
            self.option=self.phone_book_com_select_menu_list
            self.phone_book_com_select_title.set_text("电话本")
            self.phone_book_com_select_sure_label.set_text("选择")
            self.phone_book_com_select_back_label.set_text("返回")
        else:#英文显示
            self.option=self.phone_book_com_select_menu_list_en
            self.phone_book_com_select_title.set_text("Phone Book")
            self.phone_book_com_select_sure_label.set_text("Select")
            self.phone_book_com_select_sure_label.set_size(48, 12)
            self.phone_book_com_select_back_label.set_text("Back")
        for idx, item in enumerate(self.option):
            phone_book_com_select_list_btn = lv.btn(self.phone_book_com_select_list)
            phone_book_com_select_list_btn.set_pos(0, 1)
            phone_book_com_select_list_btn.set_size(115, 16)
            phone_book_com_select_list_btn.add_style(style_list_black, lv.PART.MAIN | lv.STATE.DEFAULT)
            phone_book_com_select_list_label = lv.label(phone_book_com_select_list_btn)
            phone_book_com_select_list_label.center()
            phone_book_com_select_list_label.set_pos(0, 2)
            phone_book_com_select_list_label.set_size(115, 16)
            phone_book_com_select_list_label.set_text(item)
            phone_book_com_select_list_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
            self.btn_list.append((phone_book_com_select_list_btn, phone_book_com_select_list_label))
        self.add_state()
        
    def add_state(self, cur=None):
        if cur is None:
            cur = self.cur
        self.currentButton = self.phone_book_com_select_list.get_child(cur)
        self.currentButton.set_style_bg_color(lv.color_make(0xff, 0xff, 0xff), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.set_style_bg_grad_color(lv.color_make(0xff, 0xff, 0xff), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.btn_list[cur][1].set_style_text_color(lv.color_make(0x00, 0x00, 0x00), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.scroll_to_view(lv.ANIM.OFF)

    def clear_state(self, cur=None):
        if cur is None:
            cur = self.cur
        self.currentButton = self.phone_book_com_select_list.get_child(cur)
        self.currentButton.set_style_bg_color(lv.color_make(0x00, 0x00, 0x00), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.set_style_bg_grad_color(lv.color_make(0x00, 0x00, 0x00), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.btn_list[cur][1].set_style_text_color(lv.color_make(0xff, 0xff, 0xff), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.scroll_to_view(lv.ANIM.OFF)
              
    def btn_menu_click(self):
        """确认选择按键"""
        meta_info = self.meta_info
        if self.prev_screen == 16 or self.prev_screen == 18:  #通讯录和草稿箱通讯录
            if self.cur == 0:
                phone_book_all_menu_dict = EventMesh.publish("persistent_config_get", "phone_book_all_dict")
                if not phone_book_all_menu_dict:
                    if self.language == "zh":#中文显示
                        EventMesh.publish("window_show","无电话本记录")
                    else:
                        EventMesh.publish("window_show","No PhoneBook Record")
                else:
                    EventMesh.publish("load_screen", {"screen": "phone_book_all","meta_info":meta_info})
            if self.cur == 1:
                status = sim.getStatus()
                if status == 0:
                    if self.language == "zh":#中文显示
                        EventMesh.publish("window_show", "未插SIM卡")
                    else:#英文显示
                        EventMesh.publish("window_show", "No Insert SIM Card")
                    return
                phone_book_sim_list = EventMesh.publish("readPhonebook")
                if len(phone_book_sim_list) == 0:
                    if self.language == "zh":#中文显示
                        EventMesh.publish("window_show","无电话本记录")
                    else:
                        EventMesh.publish("window_show","No PhoneBook Record")
                else:
                    meta_info["number"] = ""
                    meta_info["flag_sim_search"] = "2"
                    EventMesh.publish("load_screen", {"screen": "phone_book_sim_all","meta_info":meta_info})
        if self.prev_screen == 17:  #电话本删除全部
            if self.cur == 0:
                phone_book_all_menu_dict = EventMesh.publish("persistent_config_get", "phone_book_all_dict")
                if phone_book_all_menu_dict == None:
                    if self.language == "zh":#中文显示
                        EventMesh.publish("window_show","无电话本记录")
                    else:#英文显示
                        EventMesh.publish("window_show","No Phone Book Record")
                    return
                phone_book_all_menu_list = list(phone_book_all_menu_dict.items())
                count = len(phone_book_all_menu_list)
                if count == 0:
                    if self.language == "zh":
                        EventMesh.publish("window_show","无电话本记录")
                    else:
                        EventMesh.publish("window_show","No Phone Book Record")
                else:
                    EventMesh.publish("load_screen", {"screen": "phonebookdel","meta_info":"0"})
            if self.cur == 1:
                status = sim.getStatus()
                if status == 0:
                    if self.language == "zh":#中文显示
                        EventMesh.publish("window_show", "未插SIM卡")
                    else:#英文显示
                        EventMesh.publish("window_show", "No Insert SIM Card")
                    return
                phone_book_sim_list = EventMesh.publish("readPhonebook")
                if len(phone_book_sim_list) == 0:
                    if self.language == "zh":#中文显示
                        EventMesh.publish("window_show","无电话本记录")
                    else:
                        EventMesh.publish("window_show","No PhoneBook Record")
                else:
                    EventMesh.publish("load_screen", {"screen": "phonebookdel","meta_info":"2"})
        if self.prev_screen == 12 or self.prev_screen == 14 or self.prev_screen == 15:
            if self.cur == 0: #话机
                phone_book_all_menu_dict = EventMesh.publish("persistent_config_get", "phone_book_all_dict")
                if not phone_book_all_menu_dict:
                    if self.language == "zh":#中文显示
                        EventMesh.publish("window_show","无电话本记录")
                    else:#英文显示
                        EventMesh.publish("window_show","No Phone Book Record")
                    return
                else:
                    meta_info = {"name": self.name, "prev_screen":self.prev_screen}
                    EventMesh.publish("load_screen", {"screen": "phone_book_all","meta_info":meta_info})
            if self.cur == 1: #SIM卡
                status = sim.getStatus()
                if status == 0:
                    if self.language == "zh":#中文显示
                        EventMesh.publish("window_show", "未插SIM卡")
                    else:#英文显示
                        EventMesh.publish("window_show", "No Insert SIM Card")
                    return
                phone_book_sim_list = EventMesh.publish("readPhonebook")
                if len(phone_book_sim_list) == 0:
                    if self.language == "zh":#中文显示
                        EventMesh.publish("window_show","无电话本记录")
                    else:
                        EventMesh.publish("window_show","No PhoneBook Record")
                    return
                elif self.prev_screen == 12:
                    meta_info = {"name": self.name, "flag_sim_search": "3", "prev_screen":"12"}
                elif self.prev_screen == 14:
                    meta_info = {"name": self.name, "flag_sim_search": "4", "prev_screen":"14"}
                elif self.prev_screen == 15:
                    meta_info = {"name": self.name, "flag_sim_search": "5", "prev_screen":"15"}
                EventMesh.publish("load_screen", {"screen": "phone_book_sim_all","meta_info":meta_info})
        
    def btn_down_click(self):
        """向下选择"""
        self.clear_state()
        self.cur = self.next_idx(self.cur, self.count)
        self.add_state()

    def btn_up_click(self):
        """向上选择"""
        self.clear_state()
        self.cur = self.prev_idx(self.cur, self.count)
        self.add_state()
        
    def btn_back_click(self):
        # 返回按键
        meta_info = self.meta_info
        if self.prev_screen == 12:   #黑白名单电话本导入
            EventMesh.publish("load_screen", {"screen": "black_white", "meta_info": self.name})
        elif self.prev_screen == 14:   #快捷拨号电话本导入
            EventMesh.publish("load_screen", {"screen": "speed_dial"})
        elif self.prev_screen == 15:   #快捷键电话本导入
            EventMesh.publish("load_screen", {"screen": "quickkey"})
        elif self.prev_screen == 16:   #写短信通讯录
            self.meta_info["prev_screen"] = str(self.prev_screen_bak)
            EventMesh.publish("load_screen", {"screen": "SendMessageOption","meta_info":self.meta_info})
        if self.prev_screen == 17:   #电话本删除全部
            EventMesh.publish("load_screen", {"screen": "phone_book"})
        if self.prev_screen == 18:   #草稿箱选项界面进入通讯录
            EventMesh.publish("load_screen", {"screen":"DraftsSendMessageOption","meta_info":meta_info})
            
    def btn_pwk_click(self):
        """呼叫挂断返回通话结束页面"""
        EventMesh.publish("load_screen", {"screen": "main"})
        return True


class PhoneBookSelect(Screen):
    """
    电话本查看界面
    话机和sim卡
    """
    NAME = "phone_book_select"
    
    def __init__(self):
        super().__init__()
        self.meta = phone_book_select
        self.cur = 0
        self.count = 4
        self.phone_book_select_title = phone_book_select_title
        self.phone_book_select_list = phone_book_select_list
        self.phone_book_select_sure_label = phone_book_select_sure_label
        self.phone_book_select_back_label = phone_book_select_back_label
        self.btn_list = []
        self.currentButton = None
        self.language = "zh"
        self.phone_book_select_menu_list = [
            " 1 话机",
            " 2 SIM卡",
            " 3 SIM卡导入",
            " 4 话机导出"
        ]
        self.phone_book_select_menu_list_en = [
            " 1 Telephone",
            " 2 SIM Card",
            " 3 SIM Card Import",
            " 4 Phone Export"
            
        ]
        
    def initialization(self):
        self.btn_list = []
        self.list_create()
        return True
    
    def list_create(self):
        self.phone_book_select_list.delete()
        self.phone_book_select_list = lv.list(self.meta)
        self.phone_book_select_list.set_pos(0, 16)
        self.phone_book_select_list.set_size(128, 32)
        self.phone_book_select_list.set_style_pad_left(0, 0)
        self.phone_book_select_list.set_style_pad_top(0, 1)
        self.phone_book_select_list.set_style_pad_row(0, 0)
        self.phone_book_select_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
        self.phone_book_select_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR)
        self.language = EventMesh.publish("persistent_config_get", "language")
        if self.language == "zh":#中文显示
            self.option=self.phone_book_select_menu_list
            self.phone_book_select_title.set_text("电话本")
            self.phone_book_select_sure_label.set_text("选择")
            self.phone_book_select_back_label.set_text("返回")
        else:#英文显示
            self.option=self.phone_book_select_menu_list_en
            self.phone_book_select_title.set_text("Phone Book")
            self.phone_book_select_sure_label.set_text("Select")
            self.phone_book_select_sure_label.set_size(48, 12)
            self.phone_book_select_back_label.set_text("Back")
        for idx, item in enumerate(self.option):
            phone_book_select_list_btn = lv.btn(self.phone_book_select_list)
            phone_book_select_list_btn.set_pos(0, 1)
            phone_book_select_list_btn.set_size(115, 16)
            phone_book_select_list_btn.add_style(style_list_black, lv.PART.MAIN | lv.STATE.DEFAULT)
            phone_book_select_list_label = lv.label(phone_book_select_list_btn)
            phone_book_select_list_label.center()
            phone_book_select_list_label.set_pos(0, 2)
            phone_book_select_list_label.set_size(115, 16)
            phone_book_select_list_label.set_text(item)
            phone_book_select_list_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
            self.btn_list.append((phone_book_select_list_btn, phone_book_select_list_label))
        self.add_state()
        
    def add_state(self, cur=None):
        if cur is None:
            cur = self.cur
        self.currentButton = self.phone_book_select_list.get_child(cur)
        self.currentButton.set_style_bg_color(lv.color_make(0xff, 0xff, 0xff), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.set_style_bg_grad_color(lv.color_make(0xff, 0xff, 0xff), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.btn_list[cur][1].set_style_text_color(lv.color_make(0x00, 0x00, 0x00), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.scroll_to_view(lv.ANIM.OFF)

    def clear_state(self, cur=None):
        if cur is None:
            cur = self.cur
        self.currentButton = self.phone_book_select_list.get_child(cur)
        self.currentButton.set_style_bg_color(lv.color_make(0x00, 0x00, 0x00), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.set_style_bg_grad_color(lv.color_make(0x00, 0x00, 0x00), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.btn_list[cur][1].set_style_text_color(lv.color_make(0xff, 0xff, 0xff), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.scroll_to_view(lv.ANIM.OFF)
        
    def btn_menu_click(self):
        """确认选择按键"""
        if self.cur == 0:
             # 话机电话簿
            phone_book_all_menu_dict = EventMesh.publish("persistent_config_get", "phone_book_all_dict")
            if not phone_book_all_menu_dict:
                if self.language == "zh":#中文显示
                    EventMesh.publish("window_show","无电话本记录")
                else:
                    EventMesh.publish("window_show","No PhoneBook Record")
                utime.sleep(2)
            else:
                meta_info = {"msg":"","number":"","prev_screen":"0"}
                EventMesh.publish("load_screen", {"screen": "phone_book_all","meta_info":meta_info})
        elif self.cur == 1:
            # SIM卡电话簿
            status = sim.getStatus()
            if status == 0:
                if self.language == "zh":#中文显示
                    EventMesh.publish("window_show", "未插SIM卡")
                else:#英文显示
                    EventMesh.publish("window_show", "No Insert SIM Card")
            else:
                phone_book_sim_list = EventMesh.publish("readPhonebook")
                if len(phone_book_sim_list) == 0:
                    if self.language == "zh":#中文显示
                        EventMesh.publish("window_show","无电话本记录")
                    else:
                        EventMesh.publish("window_show","No PhoneBook Record")
                else:
                    meta_info= {"flag_sim_search": "1"}
                    EventMesh.publish("load_screen", {"screen": "phone_book_sim_all", "meta_info": meta_info})
        elif self.cur == 2:
            #sim卡导入
            #sim卡数
            status = sim.getStatus()
            if status == 0:
                if self.language == "zh":#中文显示
                    EventMesh.publish("window_show", "未插SIM卡")
                else:#英文显示
                    EventMesh.publish("window_show", "No Insert SIM Card")
            else:
                phone_book_sim_list = EventMesh.publish("readPhonebook")
                if len(phone_book_sim_list) == 0:
                    if self.language == "zh":#中文显示
                        EventMesh.publish("window_show","SIM卡无记录")
                    else:
                        EventMesh.publish("window_show","No PhoneBook Record")
                    utime.sleep(2)
                else:   
                    flag = 0
                    num = len(phone_book_sim_list)   # sim卡数 500
                    #话机数
                    phone_book_all_dict = EventMesh.publish("persistent_config_get", "phone_book_all_dict")
                    if not phone_book_all_dict:
                        count = 0
                    else:
                        phone_book_all_list = list(phone_book_all_dict.items())
                        count = len(phone_book_all_list)    #话机数 200
                    
                    if count >= 200:
                        message = "超出话机存储上限" if self.language == "zh" else "Exceeding the phone storage limit"
                        EventMesh.publish("window_show", message)
                        return
                    
                    phone_book_list = dict()
                    if (200 - count) < num:
                        flag = 200 - count
                    else:
                        flag = 0
                    EventMesh.publish("window_show", "Copying, please wait a moment")
                    for idx,(index,name,number) in enumerate(phone_book_sim_list):
                        phone_book_all_dict = EventMesh.publish("persistent_config_get", "phone_book_all_dict")
                        if phone_book_all_dict != None:
                            phone_book_list = phone_book_all_dict
                        phone_book_list.update({number:name})
                        EventMesh.publish("persistent_config_store",{"phone_book_all_dict": phone_book_list})
                        idx += 1
                        if idx == flag:
                            break
                        
                    EventMesh.publish("load_screen", {"screen": "phone_book_select"})
                    if self.language == "zh":
                        EventMesh.publish("window_show", "拷贝成功")
                    if self.language == "en":
                        EventMesh.publish("window_show", "Success copy")
        elif self.cur == 3:
            #话机导出
            status = sim.getStatus()
            if status == 0:
                if self.language == "zh":#中文显示
                    EventMesh.publish("window_show", "未插SIM卡")
                else:#英文显示
                    EventMesh.publish("window_show", "No Insert SIM Card")
            else:
                phone_book_all_dict = EventMesh.publish("persistent_config_get", "phone_book_all_dict")
                if not phone_book_all_dict or len(phone_book_all_dict)==0:
                    if self.language == "zh":#中文显示
                        EventMesh.publish("window_show","话机无记录")
                    else:
                        EventMesh.publish("window_show","No PhoneBook Record")
                else:
                    phone_book_sim_list = EventMesh.publish("readPhonebook") #SIM卡
                    num = len(phone_book_sim_list)
                    if num >= 500:
                        message = "SIM卡存储已到上限" if self.language == "zh" else "Exceeding the SIM storage limit"
                        EventMesh.publish("window_show", message)
                        return
                    phone_book_all_list = list(phone_book_all_dict.items())  #话机数量最大 200
                    count = len(phone_book_all_list)        #最大拷贝个数
                    if len(phone_book_all_list)+num > 500:  #sim里面原有的记录加上导入的超过500
                        count = 500-num
                    flag = 0
                    message = "拷贝中...请稍等" if self.language == "zh" else "Copying...Wait a moment"
                    EventMesh.publish("window_show",message)
                    utime.sleep(2)
                    for idx,(number,name) in enumerate(phone_book_all_list):
                        if idx >= count:
                            break
                        ret = EventMesh.publish("writePhonebook",[name,number])
                        if ret != 0:
                            flag = -1
                            message = "拷贝失败" if self.language == "zh" else "Failed copy"
                            break
                    
                    if flag == 0:
                        message = "拷贝成功" if self.language == "zh" else "Success copy"
                        EventMesh.publish("window_show",message)
                        # utime.sleep(2)
                        # EventMesh.publish("load_screen", {"screen": "phone_book_select"})
                    else:
                        EventMesh.publish("window_show",message)

    def btn_down_click(self):
        """向下选择"""
        self.clear_state()
        self.cur = self.next_idx(self.cur, self.count)
        self.add_state()

    def btn_up_click(self):
        """向上选择"""
        self.clear_state()
        self.cur = self.prev_idx(self.cur, self.count)
        self.add_state()
        
    def btn_back_click(self):
        # 返回按键
        EventMesh.publish("load_screen", {"screen": "phone_book"})

    def btn_pwk_click(self):
        """呼叫挂断返回通话结束页面"""
        EventMesh.publish("load_screen", {"screen": "main"})
        return True
    

class PhoneBookStoreSelect(Screen):
    """
    电话本选择界面
    话机和sim卡存储
    """
    NAME = "phone_book_store_select"
    
    def __init__(self):
        super().__init__()
        self.meta = phone_book_store_select
        self.cur = 0
        self.count = 2
        self.phone_book_store_select_list = phone_book_store_select_list
        self.phone_book_store_select_title = phone_book_store_select_title
        self.phone_book_store_select_sure_label = phone_book_store_select_sure_label
        self.phone_book_store_select_back_label = phone_book_store_select_back_label
        self.btn_list = []
        self.currentButton = None
        self.language = "zh"
        self.phone_book_store_select_menu_list = [
            " 1 话机",
            " 2 SIM卡"
        ]
        self.phone_book_store_select_menu_list_en = [
            " 1 Telephone",
            " 2 SIM Card"
        ]
        
    def initialization(self):
        self.btn_list = []
        self.list_create()
        self.prev_screen = int(self.meta_info.get("prev_screen"))
        self.back = self.meta_info.get("back")
        return True
    
    def list_create(self):
        self.phone_book_store_select_list.delete()
        self.phone_book_store_select_list = lv.list(self.meta)
        self.phone_book_store_select_list.set_pos(0, 16)
        self.phone_book_store_select_list.set_size(128, 32)
        self.phone_book_store_select_list.set_style_pad_left(0, 0)
        self.phone_book_store_select_list.set_style_pad_top(0, 1)
        self.phone_book_store_select_list.set_style_pad_row(0, 0)
        self.phone_book_store_select_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
        self.phone_book_store_select_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR)
        self.language = EventMesh.publish("persistent_config_get", "language")
        if self.language == "zh":#中文显示
            self.option=self.phone_book_store_select_menu_list
            self.phone_book_store_select_title.set_text("存储选择")
            self.phone_book_store_select_sure_label.set_text("选择")
            self.phone_book_store_select_back_label.set_text("返回")
        else:#英文显示
            self.option=self.phone_book_store_select_menu_list_en
            self.phone_book_store_select_title.set_text("Store Select")
            self.phone_book_store_select_sure_label.set_text("Select")
            self.phone_book_store_select_sure_label.set_size(48, 12)
            self.phone_book_store_select_back_label.set_text("Back")
        for idx, item in enumerate(self.option):
            phone_book_store_select_list_btn = lv.btn(self.phone_book_store_select_list)
            phone_book_store_select_list_btn.set_pos(0, 1)
            phone_book_store_select_list_btn.set_size(115, 16)
            phone_book_store_select_list_btn.add_style(style_list_black, lv.PART.MAIN | lv.STATE.DEFAULT)
            phone_book_store_select_list_label = lv.label(phone_book_store_select_list_btn)
            phone_book_store_select_list_label.center()
            phone_book_store_select_list_label.set_pos(0, 2)
            phone_book_store_select_list_label.set_size(115, 16)
            phone_book_store_select_list_label.set_text(item)
            phone_book_store_select_list_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
            self.btn_list.append((phone_book_store_select_list_btn, phone_book_store_select_list_label))
        self.add_state()
        
    def add_state(self, cur=None):
        if cur is None:
            cur = self.cur
        self.currentButton = self.phone_book_store_select_list.get_child(cur)
        self.currentButton.set_style_bg_color(lv.color_make(0xff, 0xff, 0xff), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.set_style_bg_grad_color(lv.color_make(0xff, 0xff, 0xff), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.btn_list[cur][1].set_style_text_color(lv.color_make(0x00, 0x00, 0x00), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.scroll_to_view(lv.ANIM.OFF)

    def clear_state(self, cur=None):
        if cur is None:
            cur = self.cur
        self.currentButton = self.phone_book_store_select_list.get_child(cur)
        self.currentButton.set_style_bg_color(lv.color_make(0x00, 0x00, 0x00), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.set_style_bg_grad_color(lv.color_make(0x00, 0x00, 0x00), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.btn_list[cur][1].set_style_text_color(lv.color_make(0xff, 0xff, 0xff), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.scroll_to_view(lv.ANIM.OFF)
        
    def btn_menu_click(self):
        """确认选择按键"""
        cur_screen = EventMesh.publish("persistent_config_get", "main_dialed_call")
        if self.cur == 0:
            #话机存储
            if self.prev_screen == 19 or self.prev_screen == 20 or self.prev_screen == 21:
                if cur_screen :
                    self.phone_number = self.meta_info.get("number")
                    meta_info = {"number":self.phone_number,"prev_screen":str(self.prev_screen)}
                EventMesh.publish("load_screen", {"screen": "NewMessageScreen","meta_info":self.meta_info})
            else:
                meta_info = {"msg":"","number":"","prev_screen":"4"}
                EventMesh.publish("load_screen", {"screen": "NewMessageScreen","meta_info":meta_info})
        if self.cur == 1:
            #sim卡存储
            status = sim.getStatus()
            if status == 0:
                if self.language == "zh":#中文显示
                    EventMesh.publish("window_show", "未插SIM卡")
                else:#英文显示
                    EventMesh.publish("window_show", "No Insert SIM Card")
                return
            if self.prev_screen == 19 or self.prev_screen == 20 or self.prev_screen == 21:
                self.phone_number = self.meta_info.get("number")
                if cur_screen :
                    meta_info = {"number":self.phone_number,"prev_screen":str(self.prev_screen),"sim":"yes"}
                else:
                    meta_info = {"number":self.phone_number,"prev_screen":str(self.prev_screen),"sim":"yes","back":self.back}
                EventMesh.publish("load_screen", {"screen": "NewMessageScreen","meta_info":meta_info})
            else:
                meta_info = {"prev_screen":"13"}
                EventMesh.publish("load_screen", {"screen": "NewMessageScreen","meta_info":meta_info})            
        
    def btn_down_click(self):
        """向下选择"""
        self.clear_state()
        self.cur = self.next_idx(self.cur, self.count)
        self.add_state()

    def btn_up_click(self):
        """向上选择"""
        self.clear_state()
        self.cur = self.prev_idx(self.cur, self.count)
        self.add_state()
        
    def btn_back_click(self):
        # 返回按键
        if self.prev_screen == 20:  #收件箱添加联系人
            EventMesh.publish("load_screen", {"screen": "InboxOptionsScreen"})
        elif self.prev_screen == 21:  #拨号添加联系人
            EventMesh.publish("load_screen", {"screen": "dialoption"})
        elif self.prev_screen == 4 or self.prev_screen == 13: #新建电话本添加联系人 话机 sim卡
            EventMesh.publish("load_screen", {"screen": "phone_book"})
        else:  #19  #通话记录添加联系人
            EventMesh.publish("load_screen", {"screen": "call_log_options"})

    def btn_pwk_click(self):
        """呼叫挂断返回通话结束页面"""
        EventMesh.publish("load_screen", {"screen": "main"})
        return True
    
    
#电话本删除界面
class PhoneBookDeleteScreen(Screen):

    NAME = "phonebookdel"

    def __init__(self):
        super().__init__()
        self.cursor_key_id = 0
        self.meta = phonebook_delete_screen
        self.phonebook_delete_screen_label = phonebook_delete_screen_label
        self.phonebook_delete_screen_sure = phonebook_delete_screen_sure
        self.phonebook_delete_screen_return = phonebook_delete_screen_return
        self.language = "zh"

    def initialization(self):
        self.language = EventMesh.publish("persistent_config_get", "language")
        self.cursor_key_id = int(self.meta_info)
        if self.language == "zh":
            self.phonebook_delete_screen_label.set_text("确认删除吗?")
            self.phonebook_delete_screen_sure.set_text("确定")
            self.phonebook_delete_screen_return.set_size(24, 12)
            self.phonebook_delete_screen_return.set_text("取消")
        else:
            self.phonebook_delete_screen_label.set_pos(2, 26)
            self.phonebook_delete_screen_label.set_long_mode(lv.label.LONG.WRAP)
            self.phonebook_delete_screen_label.set_text("Confirm The Deletion?")
            self.phonebook_delete_screen_sure.set_text("OK")
            self.phonebook_delete_screen_return.set_pos(80, 51)
            self.phonebook_delete_screen_return.set_size(48, 12)
            self.phonebook_delete_screen_return.set_text("Cancel")
        return True

    def btn_menu_click(self):
        #确认按键
        print("删除")
        self.language = EventMesh.publish("persistent_config_get", "language")
        if self.cursor_key_id == 0:
            if self.language == "zh":
                EventMesh.publish("window_show","删除电话本完成")
            else:
                EventMesh.publish("window_show","The Deletion Is Complete")
            EventMesh.publish("persistent_config_store", {"phone_book_all_dict": {}})
            utime.sleep(2)
            EventMesh.publish("load_screen", {"screen": "phone_book_com_select"})
        if self.cursor_key_id == 1:
            if self.language == "zh":
                EventMesh.publish("window_show","删除通话记录完成")
            else:
                EventMesh.publish("window_show","The Deletion Is Complete")
            EventMesh.publish("persistent_config_store", {"call_logs_list": []})
            EventMesh.publish("persistent_config_store", {"missed_call": 0})
            EventMesh.publish("persistent_config_store", {"received_call": 0})
            EventMesh.publish("persistent_config_store", {"dialed_call": 0})
            utime.sleep(2)
            EventMesh.publish("load_screen", {"screen": "calls"})
        if self.cursor_key_id == 2:   #SIM卡删除全部
            message = "删除中...请稍等" if self.language == "zh" else "Deleting...Wait a moment"
            EventMesh.publish("window_show",message)
            utime.sleep(2)
            ret = EventMesh.publish("delPhonebook",[1000, 1000])
            if ret == -1:
                if self.language == "zh":#中文显示
                    EventMesh.publish("window_show","删除电话本失败")
                else:
                    EventMesh.publish("window_show","Failed to delete phone book")
            elif ret == 0:
                if self.language == "zh":#中文显示
                    EventMesh.publish("window_show","删除电话本完成")
                else:
                    EventMesh.publish("window_show","The Deletion Is Complete")
            utime.sleep(2)
            EventMesh.publish("load_screen", {"screen": "phone_book_com_select"})
    def btn_back_click(self):
        # 返回按键
        if self.cursor_key_id == 0:
            EventMesh.publish("load_screen", {"screen": "phone_book_com_select"})
        if self.cursor_key_id == 1:
            EventMesh.publish("load_screen", {"screen": "calls"})
        if self.cursor_key_id == 2:
            EventMesh.publish("load_screen", {"screen": "phone_book_com_select"})
            
    def btn_pwk_click(self):
        """呼叫挂断返回通话结束页面"""
        EventMesh.publish("load_screen", {"screen": "main"})
        return True

class PhoneBookAllScreen(Screen):
    """
       电话本查看全部页面
    """
    NAME = "phone_book_all"

    def __init__(self):
        super().__init__()
        self.meta = phone_book_all_screen
        self.cur = 0
        self.count = 0
        self.language = "zh"
        self.phone_book_all_list = phone_book_all_list
        self.phone_book_all_options_label = phone_book_all_options_label
        self.phone_book_all_back_label = phone_book_all_back_label
        self.phone_book_all_memory_value=phone_book_all_memory_value
        self.btn_list = []
        self.prev_screen = 0
        self.flag_copy = 0
        self.currentButton = None
        self.phone_book_all_menu_list = []
        self.name = None

    def post_processor_after_instantiation(self):
        """注册后台调用事件"""
        EventMesh.subscribe("get_phone_number_name", self.get_phone_number_name)
        EventMesh.subscribe("import_from_phonebook", self.import_from_phonebook)

    def initialization(self):
        self.cur = 0
        self.btn_list = []
        self.prev_screen = 0
        self.language = EventMesh.publish("persistent_config_get", "language")
        if self.meta_info != None:
            self.prev_screen = int(self.meta_info.get("prev_screen"))
            if self.meta_info.get("name") != None:
                self.name = self.meta_info.get("name")
            if self.prev_screen == 0 or self.prev_screen == 5:
                if self.language == "zh":
                    self.phone_book_all_options_label.set_text("选项")
                    self.phone_book_all_back_label.set_text("返回")
                else:
                    self.phone_book_all_options_label.set_text("Options")
                    self.phone_book_all_options_label.set_size(48, 12)
                    self.phone_book_all_back_label.set_text("Back")
            if self.prev_screen == 1 or self.prev_screen == 16:  # 16 通讯录
                if self.language == "zh":
                    self.phone_book_all_options_label.set_text("发送")
                    self.phone_book_all_back_label.set_text("返回")
                else:
                    self.phone_book_all_options_label.set_text("Send")
                    self.phone_book_all_back_label.set_text("Back")
            if self.prev_screen == 12 or self.prev_screen == 14 or self.prev_screen == 15:
                if self.language == "zh":
                    self.phone_book_all_options_label.set_text("选择")
                    self.phone_book_all_back_label.set_text("返回")
                else:
                    self.phone_book_all_options_label.set_text("Select")
                    self.phone_book_all_options_label.set_size(48, 12)
                    self.phone_book_all_back_label.set_text("Back")
            if self.prev_screen == 5:  #电话本搜索
                phone_book_search_dict = EventMesh.publish("persistent_config_get", "phone_book_search")
                phone_book_search_list = list(phone_book_search_dict.items())
                self.phone_book_all_menu_list = phone_book_search_list
                self.count = len(self.phone_book_all_menu_list)
                self.list_create()
                phone_book_all_memory_value.set_text(str(self.count)+"/200")
            else: #0 16 18 # 0话机查看电话本     16通讯录  18草稿箱通讯录    #写短信 转发 回复等发送进入通讯录   #电话本导入
                phone_book_list = list(EventMesh.publish("persistent_config_get", "phone_book_all_dict").items())
                self.phone_book_all_menu_list = self.order_phone_book_list(phone_book_list)
                self.count = len(self.phone_book_all_menu_list)
                self.list_create()
                phone_book_all_memory_value.set_text(str(self.count)+"/200")                    
        return True
    
    def order_phone_book_list(self,phone_book_table):
        ret_phone_book_list = list(phone_book_table)
        list_len = len(phone_book_table)
        for i in range(list_len):
            index = 0
            phone_book = phone_book_table[i]
            for idx,item in enumerate(phone_book_table):
                if item[1] < phone_book[1]:
                    index += 1
                elif item[1] == phone_book[1]:
                    if item[0] < phone_book[0]:
                        index += 1
            ret_phone_book_list[index] = phone_book_table[i]
        return ret_phone_book_list
    
    def get_phone_number_name(self, topic=None, number=None):
        """查询电话号码姓名"""
        phone_book_all_menu_dict = EventMesh.publish("persistent_config_get", "phone_book_all_dict")
        if len(phone_book_all_menu_dict) != {}:
            name_phone = phone_book_all_menu_dict.get(number, "")
            if name_phone != "":
                return name_phone
            else:
                phone_book_sim_menu_dict = EventMesh.publish("readPhonebook")
                for idx,item in enumerate(phone_book_sim_menu_dict):
                    if item[2] == number:
                        return item[1]
                return ""

            
    def list_create(self):
        self.phone_book_all_list.delete()
        self.phone_book_all_list = lv.list(self.meta)
        self.phone_book_all_list.set_pos(0, 0)
        self.phone_book_all_list.set_size(128, 50)
        self.phone_book_all_list.set_style_pad_left(0, 0)
        self.phone_book_all_list.set_style_pad_top(0, 0)
        self.phone_book_all_list.set_style_pad_row(1, 0)
        self.phone_book_all_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
        self.phone_book_all_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR)
        self.phone_book_all_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.SCROLLED)
        for idx, item in enumerate(self.phone_book_all_menu_list):
            phone_book_all_list_btn = lv.btn(self.phone_book_all_list)
            phone_book_all_list_btn.set_pos(0, 0)
            phone_book_all_list_btn.set_size(115, 16)
            phone_book_all_list_btn.add_style(style_list_black, lv.PART.MAIN | lv.STATE.DEFAULT)
            phone_book_all_list_label = lv.label(phone_book_all_list_btn)
            phone_book_all_list_label.set_pos(10, 2)
            phone_book_all_list_label.set_size(115, 14)
            if item[1] == "":
                msg = item[0]
            else:
                msg = item[1]
            phone_book_all_list_label.set_text(msg)
            self.btn_list.append((phone_book_all_list_btn, phone_book_all_list_label))
        self.add_state()

    def add_state(self, cur=None):
        if cur is None:
            cur = self.cur
        self.currentButton = self.phone_book_all_list.get_child(cur)
        self.currentButton.set_style_bg_color(lv.color_make(0xff, 0xff, 0xff), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.set_style_bg_grad_color(lv.color_make(0xff, 0xff, 0xff), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.btn_list[cur][1].set_style_text_color(lv.color_make(0x00, 0x00, 0x00), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.scroll_to_view(lv.ANIM.OFF)

    def clear_state(self, cur=None):
        if cur is None:
            cur = self.cur
        self.currentButton = self.phone_book_all_list.get_child(cur)
        self.currentButton.set_style_bg_color(lv.color_make(0x00, 0x00, 0x00), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.set_style_bg_grad_color(lv.color_make(0x00, 0x00, 0x00), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.btn_list[cur][1].set_style_text_color(lv.color_make(0xff, 0xff, 0xff), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.scroll_to_view(lv.ANIM.OFF)

    def btn_menu_click(self):
        # 选择按键
        self.language = EventMesh.publish("persistent_config_get", "language")
        if self.prev_screen == 0:
            flag_sim = 0
            meta_info = [self.phone_book_all_menu_list[self.cur], self.cur, flag_sim]
            EventMesh.publish("load_screen", {"screen": "phone_book_search_options", "meta_info": meta_info})
        elif self.prev_screen == 1 or self.prev_screen == 16 or self.prev_screen == 18:   #通讯录
            if self.prev_screen == 16:
                EventMesh.publish("persistent_config_store", {"set_messave_flag":"1"})  #发送后不用保存草稿箱
            user_info = self.phone_book_all_menu_list[self.cur]
            message = self.meta_info.get("msg"),user_info[0]
            print("message",message)
            if EventMesh.publish("message_send", message) == 0:
                if self.language=="zh":
                    EventMesh.publish("window_show", "发送成功")
                else:
                    EventMesh.publish("window_show", "Send Successfully")
                utime.sleep(2)
                if self.prev_screen == 18:
                    meta_info = self.meta_info
                    EventMesh.publish("load_screen", {"screen": "draftsdelscreen", "meta_info": meta_info})
                else: 
                    EventMesh.publish("load_screen", {"screen": "MessageScreen"})
            else:
                if self.language=="zh":
                    EventMesh.publish("window_show", "发送失败")
                else:
                    EventMesh.publish("window_show", "Send Failed")
        elif self.prev_screen == 5:
            flag_phone_book_options = 1
            meta_info = [self.phone_book_all_menu_list[self.cur], self.cur,flag_phone_book_options]
            EventMesh.publish("load_screen", {"screen": "phone_book_options", "meta_info": meta_info})
        elif self.prev_screen == 12 or self.prev_screen == 14 or self.prev_screen == 15:  #电话本导入
            user_info = self.phone_book_all_menu_list[self.cur]
            print("号码:",user_info[0])
            msg = {"index":self.prev_screen,"name":self.name,"number":user_info[0]}
            EventMesh.publish("import_from_phonebook",msg)
            
    def import_from_phonebook(self,topic=None,msg=None): #电话本导入号码
        print("import_msg:",msg)
        if not msg:
            return
        else:
            index = int(msg.get("index"))
            name = msg.get("name")
            number = msg.get("number")
        if index == 12:  #黑白名单
            blacklist = EventMesh.publish("persistent_config_get", "blacklist")
            if blacklist == None:  
                blacklist = []
            whitelist = EventMesh.publish("persistent_config_get", "whitelist")
            if whitelist == None:  
                whitelist = []
            if name == "1": #黑名单
                if number in blacklist:
                    if self.language == "zh":
                        EventMesh.publish("window_show","名单中已存在")
                    else:
                        EventMesh.publish("window_show","Already exists in the list")
                    return
                elif number in whitelist:
                    if self.language == "zh":
                        EventMesh.publish("window_show","白名单中已存在")
                    else:
                        EventMesh.publish("window_show","Already exists in the whitelist")
                    return 
                else:
                    blacklist.append(number)
                    EventMesh.publish("persistent_config_store",{"blacklist":blacklist})
            elif name == "2": #白名单
                if number in whitelist:
                    if self.language == "zh":
                        EventMesh.publish("window_show","名单中已存在")
                    else:
                        EventMesh.publish("window_show","Already exists in the list")
                    return
                elif number in blacklist:
                    if self.language == "zh":
                        EventMesh.publish("window_show","黑名单中已存在")
                    else:
                        EventMesh.publish("window_show","Already exists in the blacklist")
                    return
                else:
                    whitelist.append(number)
                    EventMesh.publish("persistent_config_store", {"whitelist":whitelist})
            EventMesh.publish("load_screen", {"screen": "black_white", "meta_info": name})
        elif index == 14:  #快捷拨号
            i = int(name)
            key = EventMesh.publish("persistent_config_get", "speed_dial_number")
            if key == None:
                key = ["","","","","","","",""]
            key[i] = number
            EventMesh.publish("persistent_config_store",{"speed_dial_number":[key[0],key[1],key[2],key[3],\
                    key[4],key[5],key[6],key[7]]})
            EventMesh.publish("load_screen", {"screen": "speed_dial"})
        elif index == 15:  #快捷键
            key = EventMesh.publish("persistent_config_get", "quikkey")
            if key == None:
                key = ["","","",""]
            i = int(name)
            key[i] = number
            EventMesh.publish("persistent_config_store",{"quikkey": [key[0],key[1],key[2],key[3]]})
            EventMesh.publish("load_screen", {"screen": "quickkey"})
            if self.language == "zh":
                EventMesh.publish("window_show","快捷键保存成功")
            else:
                EventMesh.publish("window_show","Save Successfully")

    def btn_back_click(self):
        # 返回按键
        meta_info = self.meta_info
        if self.prev_screen == 0:
            meta_info = {"prev_screen":"0"}
            EventMesh.publish("load_screen", {"screen": "phone_book_select","meta_info":meta_info})
        # elif self.prev_screen == 2:  #话机到SIM卡拷贝 
        #     EventMesh.publish("load_screen", {"screen": "phone_book_select"})
        elif self.prev_screen == 5:
            EventMesh.publish("load_screen", {"screen": "phone_book_search_options"})
        elif self.prev_screen == 12 or self.prev_screen == 14 or self.prev_screen == 15:
            EventMesh.publish("load_screen", {"screen": "phone_book_com_select","meta_info":meta_info})
        elif self.prev_screen == 16 or self.prev_screen == 18:
            EventMesh.publish("load_screen", {"screen": "phone_book_com_select","meta_info":meta_info})

    def btn_up_click(self):
        self.clear_state()
        self.cur = self.prev_idx(self.cur, self.count)
        self.add_state()

    def btn_down_click(self):
        self.clear_state()
        self.cur = self.next_idx(self.cur, self.count)
        self.add_state()

class PhoneBookSimAllScreen(Screen): 
    """
       sim卡电话本查看全部页面
    """
    NAME = "phone_book_sim_all"

    def __init__(self):
        super().__init__()
        self.meta = phone_book_sim_all_screen
        self.cur = 0
        self.count = 0
        self.language = "zh"
        self.flag_sim_search = 0
        self.phone_book_sim_all_list = phone_book_sim_all_list  
        self.phone_book_all_sim_options_label = phone_book_all_sim_options_label
        self.phone_book_all_sim_back_label = phone_book_all_sim_back_label
        self.phone_book_all_sim_memory_value=phone_book_all_sim_memory_value
        self.name = ""
        self.btn_list = []
        self.currentButton = None
        self.phone_book_sim_all_menu_list = []
        
    def initialization(self):
        self.cur = 0
        self.btn_list = []
        self.flag_sim_search = int(self.meta_info.get("flag_sim_search"))
        if self.meta_info.get("name"):
           self.name = self.meta_info.get("name")
        self.language = EventMesh.publish("persistent_config_get", "language")
        if self.flag_sim_search == 0 or self.flag_sim_search == 1:  
            if self.language == "zh":
                self.phone_book_all_sim_options_label.set_text("选项")
                self.phone_book_all_sim_back_label.set_text("返回")
            else:
                self.phone_book_all_sim_options_label.set_text("Select")
                self.phone_book_all_sim_options_label.set_size(48, 12)
                self.phone_book_all_sim_back_label.set_text("Back")
        if self.flag_sim_search == 2:  # 2-新短信通讯录
            if self.language == "zh":
                self.phone_book_all_sim_options_label.set_text("发送")
                self.phone_book_all_sim_back_label.set_text("返回")
            else:
                self.phone_book_all_sim_options_label.set_text("Send")
                self.phone_book_all_sim_back_label.set_text("Back")
        else:
            if self.language == "zh":
                self.phone_book_all_sim_options_label.set_text("选择")
                self.phone_book_all_sim_back_label.set_text("返回")
            else:
                self.phone_book_all_sim_options_label.set_text("Select")
                self.phone_book_all_sim_options_label.set_size(48, 12)
                self.phone_book_all_sim_back_label.set_text("Back")

        if self.flag_sim_search == 0:  #SIM卡搜索后电话本
            phone_book_sim_search_dict = EventMesh.publish("persistent_config_get", "phone_book_sim_search")
            phone_book_sim_search_list = list(phone_book_sim_search_dict.items())
            self.phone_book_sim_all_menu_list = phone_book_sim_search_list
            self.count = len(self.phone_book_sim_all_menu_list)
            if self.count == None:
                if self.language == "zh":
                    EventMesh.publish("window_show","无电话本记录")
                else:
                    EventMesh.publish("window_show","No phone book records")
            else:
                self.list_create()
                phone_book_all_sim_memory_value.set_text(str(self.count)+"/500")
        else:  # 1-SIM卡选项搜索前电话本 3-5分别为黑白名单,快捷拨号和快捷键电话本导入
            phone_book_sim_list = EventMesh.publish("readPhonebook")
            self.phone_book_sim_all_menu_list = phone_book_sim_list
            self.count = len(self.phone_book_sim_all_menu_list)
            if self.count == None:
                if self.language == "zh":
                    EventMesh.publish("window_show","无电话本记录")
                else:
                    EventMesh.publish("window_show","No phone book records")
            else:
                self.list_create()
                phone_book_all_sim_memory_value.set_text(str(self.count)+"/500")
        return True
   
        
    def list_create(self):
        self.phone_book_sim_all_list.delete()
        self.phone_book_sim_all_list = lv.list(self.meta)
        self.phone_book_sim_all_list.set_pos(0, 0)
        self.phone_book_sim_all_list.set_size(128, 50)
        self.phone_book_sim_all_list.set_style_pad_left(0, 0)
        self.phone_book_sim_all_list.set_style_pad_top(0, 0)
        self.phone_book_sim_all_list.set_style_pad_row(1, 0)
        self.phone_book_sim_all_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
        self.phone_book_sim_all_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR)
        self.phone_book_sim_all_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR | lv.STATE.SCROLLED)
        for idx, item in enumerate(self.phone_book_sim_all_menu_list):
            phone_book_sim_all_list_btn = lv.btn(self.phone_book_sim_all_list)
            phone_book_sim_all_list_btn.set_pos(0, 0)
            phone_book_sim_all_list_btn.set_size(115, 16)
            phone_book_sim_all_list_btn.add_style(style_list_black, lv.PART.MAIN | lv.STATE.DEFAULT)
            phone_book_sim_all_list_label = lv.label(phone_book_sim_all_list_btn)
            phone_book_sim_all_list_label.set_pos(10, 2)
            phone_book_sim_all_list_label.set_size(115, 14)
            if self.flag_sim_search == 0:   #sim卡搜索后
                if item[1][1] == "":
                    msg = item[1][2]
                else:
                    msg = item[1][0]
            else:  #1 sim卡搜索前 
                if item[1] == "":
                    msg = item[2]
                else:
                    msg = item[1]
            phone_book_sim_all_list_label.set_text(msg)
            self.btn_list.append((phone_book_sim_all_list_btn, phone_book_sim_all_list_label))
        self.add_state()

    def add_state(self, cur=None):
        if cur is None:
            cur = self.cur
        self.currentButton = self.phone_book_sim_all_list.get_child(cur)
        self.currentButton.set_style_bg_color(lv.color_make(0xff, 0xff, 0xff), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.set_style_bg_grad_color(lv.color_make(0xff, 0xff, 0xff), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.btn_list[cur][1].set_style_text_color(lv.color_make(0x00, 0x00, 0x00), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.scroll_to_view(lv.ANIM.OFF)

    def clear_state(self, cur=None):
        if cur is None:
            cur = self.cur
        self.currentButton = self.phone_book_sim_all_list.get_child(cur)
        self.currentButton.set_style_bg_color(lv.color_make(0x00, 0x00, 0x00), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.set_style_bg_grad_color(lv.color_make(0x00, 0x00, 0x00), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.btn_list[cur][1].set_style_text_color(lv.color_make(0xff, 0xff, 0xff), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.scroll_to_view(lv.ANIM.OFF)
        
    def btn_menu_click(self):
        # 选择按键~
        if self.flag_sim_search == 1: 
            flag_sim = 1
            menu_cur = (self.phone_book_sim_all_menu_list[self.cur][2], self.phone_book_sim_all_menu_list[self.cur][1], self.phone_book_sim_all_menu_list[self.cur][0])
            meta_info = [menu_cur, self.cur, flag_sim]
            EventMesh.publish("load_screen", {"screen": "phone_book_search_options", "meta_info": meta_info})
        if self.flag_sim_search == 0: 
            flag_phone_book_options = 0
            menu_cur = (self.phone_book_sim_all_menu_list[self.cur][1][1], self.phone_book_sim_all_menu_list[self.cur][1][0],self.phone_book_sim_all_menu_list[self.cur][1][2])
            meta_info = [menu_cur, self.cur, flag_phone_book_options]
            EventMesh.publish("load_screen", {"screen": "phone_book_options", "meta_info": meta_info})
        if self.flag_sim_search == 2:   #新短信通讯录
            if self.meta_info.get("prev_screen")=="16":
                EventMesh.publish("persistent_config_store", {"set_messave_flag":"1"})  #发送短信后不用保存草稿箱
            number = self.phone_book_sim_all_menu_list[self.cur][2]
            msg = self.meta_info.get("msg")
            message = msg,number
            if len(self.meta_info.get("msg")) == 0:
                    message = "内容为空" if self.language == "zh" else "Content is empty"
            else:
                if EventMesh.publish("message_send", message) == 0:
                    message = "发送成功" if self.language == "zh" else "Send successfully"
                else:
                    message = "发送失败" if self.language == "zh" else "Failed to send"
            EventMesh.publish("window_show", message)
            utime.sleep(2)
            EventMesh.publish("load_screen", {"screen": "MessageScreen"})
        
        if self.flag_sim_search >= 3 and self.flag_sim_search <= 5:
            number = self.phone_book_sim_all_menu_list[self.cur][2]
            if self.flag_sim_search == 3:   #黑白名单
                msg = {"index":"12","name":self.name,"number":number}
            elif self.flag_sim_search == 4:    #快捷拨号
                msg = {"index":"14","name":self.name,"number":number}
            elif self.flag_sim_search == 5:    #快捷键
                msg = {"index":"15","name":self.name,"number":number}
            EventMesh.publish("import_from_phonebook",msg)

    def btn_back_click(self):
        # 返回按键   
        if self.flag_sim_search == 1: 
            EventMesh.publish("load_screen", {"screen": "phone_book_select"})
        elif self.flag_sim_search == 0: 
            # meta_info = {"flag_sim_search": "1"}
            EventMesh.publish("load_screen", {"screen": "phone_book_search_options"})
        else:   
            EventMesh.publish("load_screen", {"screen": "phone_book_com_select","meta_info":self.meta_info})


    def btn_up_click(self):
        self.clear_state()
        self.cur = self.prev_idx(self.cur, self.count)
        self.add_state()

    def btn_down_click(self):
        self.clear_state()
        self.cur = self.next_idx(self.cur, self.count)
        self.add_state()
        
        
class LcdStateMachine(object):

    def __init__(self, gpio=None ):
        self.gpio = Pin(Pin.GPIO38 , Pin.OUT, Pin.PULL_DISABLE, 1)
        print("灭屏？3")
        self.sleep_timer = osTimer()
        self.timer_started = 0
        EventMesh.subscribe("wake_up", self.wake_up)
        EventMesh.subscribe("backlight_control"  ,self.set_backlight_pin)
        EventMesh.subscribe("backlight_get_state",self.get_backlight_pin)
        
    def __timer_start(self):
        lcd_sleep_time = EventMesh.publish("persistent_config_get", "backlight")
        if lcd_sleep_time == None:
            i_sleep_time = 15
        else:
            i_sleep_time = int(lcd_sleep_time)
        if EventMesh.publish("get_IsgoTomain","0")==0:
            i_sleep_time = 30
        if self.timer_started == 1:
            self.sleep_timer.stop()
        self.timer_started = 1
        self.sleep_timer.start(i_sleep_time*1000, 0, self.enable_auto_sleep)

    def timer_restart(self):
        # print("LcdStateMachine timer_restart started:",self.timer_started)
        if self.timer_started == 1:
            self.sleep_timer.stop()
        self.timer_started = 1
        self.__timer_start()

    def wake_up(self,topic=None, data=None):
        if EventMesh.publish("get_IsgoTomain","0") == 0:
            # 关机充电状态唤醒后重新计时
            self.__timer_start()
            return
        # print("backlight_switch====",EventMesh.publish("persistent_config_get","backlight_switch"))
        if EventMesh.publish("persistent_config_get","backlight_switch")==1:
            # print("开灯!!!!!!!")
            self.gpio.write(1)
        else:
            self.gpio.write(0)
        # 第一个参数0 进入休眠 1 退出休眠，第二个参数是给临时休眠唤醒用的
        EventMesh.publish("lower_power", ["1","0"])
        self.__timer_start()

    def set_backlight_pin(self,topic=None, data=None):

        self.gpio.write(int(data))

    def get_backlight_pin(self,topic=None, data=None):

        return self.gpio.read()

    def enable_auto_sleep(self, *args):
        self.timer_started = 0
        if EventMesh.publish("get_IsgoTomain","0") == 0:
            # 关机充电休眠只把背光灯关掉
            self.gpio.write(0)
            return
        if EventMesh.publish("get_call_state") != "0" or EventMesh.publish("get_current_screen_name") == "phone_book_select" or EventMesh.publish("get_current_screen_name") == "phonebookdel": 
            self.timer_restart()
            return
        if EventMesh.publish("get_charging_state") == 1:
            self.gpio.write(0)
            self.timer_restart()
            return
        self.gpio.write(0)
        # 第一个参数0 进入休眠 1 退出休眠，第二个参数是给临时休眠唤醒用的
        EventMesh.publish("lower_power", ["0","0"])

class UiControls(Abstract):
    """
    UI页面管理与切换
    """
    def __init__(self):
        super().__init__()
        self.key = []
        self.screen_list = []
        self.msg_box_list = []
        self.cur_screen = None
        self.lv = lv
        self.lcd_state_machine = LcdStateMachine()
        self.call_phone_number = ""
        self.num_len = 0
        self.temp=0
        self.language = "zh"
        self.LCD_SX=osTimer()
        self._lcd = LCD()
        self.usb = USB()
        self.Audio=audio.Audio(0)
        self.hook_click_num = 0
        self.load_lock=_thread.allocate_lock()
    def post_processor_after_instantiation(self, *args, **kwargs):
        # ui 页面加载方法
        EventMesh.subscribe("load_screen", self.load_screen)
        EventMesh.subscribe("get_input_method", self.get_input_method)
        EventMesh.subscribe("btn_symbol_click", self.btn_click)
        EventMesh.subscribe("btn_menu_click", self.btn_click)
        EventMesh.subscribe("btn_back_click", self.btn_click)
        EventMesh.subscribe("btn_num_click", self.btn_click)
        EventMesh.subscribe("btn_up_click", self.btn_click)
        EventMesh.subscribe("btn_down_click", self.btn_click)
        EventMesh.subscribe("btn_left_click", self.btn_click)
        EventMesh.subscribe("btn_right_click", self.btn_click)
        EventMesh.subscribe("btn_fast_1_click", self.btn_click)
        EventMesh.subscribe("btn_fast_2_click", self.btn_click)
        EventMesh.subscribe("btn_fast_3_click", self.btn_click)
        EventMesh.subscribe("btn_fast_4_click", self.btn_click)
        EventMesh.subscribe("btn_hands_free_click", self.btn_click)
        EventMesh.subscribe("btn_headset_click", self.btn_click)
        EventMesh.subscribe("btn_redial_click", self.btn_click)
        EventMesh.subscribe("btn_call_click", self.btn_click)
        EventMesh.subscribe("btn_pwk_click", self.btn_click)
        EventMesh.subscribe("btn_pwk_long_click", self.btn_click)
        EventMesh.subscribe("btn_num_long_click", self.btn_click) 
        EventMesh.subscribe("btn_fast_long_click", self.btn_click)
        EventMesh.subscribe("btn_hook_click", self.btn_click)
        EventMesh.subscribe("get_current_screen_name", self.get_current_screen_name)
        EventMesh.subscribe("hook_click_flag", self.hook_click_flag) 
        EventMesh.subscribe("power_close", self.power_close) 
        # EventMesh.subscribe("pwk_press_handle", self.btn_click)
        # EventMesh.subscribe("pwk_up_handle", self.btn_click)
        EventMesh.subscribe("lcd_backlight_handle", self.btn_click) 
        for box in self.msg_box_list:
            box.post_processor_after_instantiation()
        for scr in self.screen_list:
            scr.post_processor_after_instantiation()
        EventMesh.publish("wake_up")
        self.LCD_SX.start(100,0,self.LCD_ST7567)
        self.LCD_SX.start(10000,1,self.LCD_ST7567)
        # self._lcd.lcd_contrast(5)
    def LCD_ST7567(self,args):
        if self.usb.getStatus()==1:
            # print("lcd刷新")
            self._lcd.lcd_write_cmd(0xF8,1)
            self._lcd.lcd_write_cmd(0x01,1)
            self._lcd.lcd_write_cmd(0x24,1)
            self._lcd.lcd_write_cmd(0x2F,1)
            self._lcd.lcd_write_cmd(0xA3,1)
            self._lcd.lcd_write_cmd(0x81,1)
            self._lcd.lcd_write_cmd(0x1A,1)
            self._lcd.lcd_write_cmd(0xA0,1)

            self._lcd.lcd_write_cmd(0xC8,1)
            self._lcd.lcd_write_cmd(0x40,1)
            self._lcd.lcd_write_cmd(0xA6,1)
            self._lcd.lcd_write_cmd(0xA4,1)
            self._lcd.lcd_write_cmd(0xB0,1)
            self._lcd.lcd_write_cmd(0xAF,1)
        else:
            print("未插电源不刷新")
    def load_screen(self, event, msg):
        """
            跳转加载界面
                1. 遍历所有的添加的界面
                2. 如果匹配到界面
                    1. 判断如果当前界面存在(不存在情况存在于开机首次界面加载的时候self.cur_screen是None的状态, 后面都不会是是None)
                        1. 如果当前界面非跳转界面不一样
                            1. 获取是否存在A界面传递到B界面的信息, 有->保存
                            2. 设置B界面的上一界面是A界面
                    2. 调用上一界面的离开方法(即软销毁)
                3. 执行初始化三部操作
                4. 赋值当前界面为跳转界面
                5. 跳转到对应界面
        @param event:
        @param msg:
        @return:
        """
        with self.load_lock:
            for scr in self.screen_list:
                if scr.NAME == msg.get("screen"):
                    if self.cur_screen:
                        if scr.NAME != self.cur_screen.NAME:
                            if self.cur_screen.NAME=="call_up" and scr.NAME in ["call_out"]:
                                print("当前界面不可跳转！！！！！！！！！")
                                return
                            meta_info = msg.get("meta_info")
                            if meta_info:
                                scr.set_meta_info(meta_info)
                            # else :
                            #     scr.set_meta_info(None)
                            scr.set_pre_screen(self.cur_screen.NAME)
                        self.cur_screen.deactivate()
                    scr.post_processor_before_initialization()
                    if scr.initialization() == False:
                        return

                    if scr.NAME == "call_out":
                        self.cur_screen = scr
                        self.lv.scr_load(self.cur_screen.meta)
                        scr.post_processor_after_initialization()
                        return

                    scr.post_processor_after_initialization()
                    self.cur_screen = scr
                    self.lv.scr_load(self.cur_screen.meta)
                    break

    def add_screen(self, screen):
        """
        所有UI页面添加到数组
        """
        self.screen_list.append(screen)
        return self

    def add_msg_box(self, msg_box):
        self.msg_box_list.append(msg_box)
        return self

    def pop_window_manage(self, event):
        """弹窗管理"""
        if event == "btn_call_click" or event == "btn_pwk_click":
            EventMesh.publish("window_hide")
            return True
        if EventMesh.publish("get_window_flag"):
            EventMesh.publish("window_hide")
            return False
        return True

    def get_input_method(self, event=None, data=None):
        try:
            return self.cur_screen.get_input_method()
        except:
            print("未定义输入模式")
            return "123"

    def btn_click(self, event, msg):
        """
        可以在这里进行拦截等
        举例: 如果设备息屏下面, 可以对按键进行拦截, 认为任何按键第一次是唤醒屏幕, 第二次才是执行
        按键判断执行, 其他类似
        """
        EventMesh.publish("wake_up")
        if not self.pop_window_manage(event):
            # 有弹窗的情况直接返回，接听按键和挂断按键不受影响
            return
        # EventMesh.publish("audio_tone_stop")
        if event == "btn_menu_click":
            self.btn_menu_click()
        elif event == "btn_back_click":
            self.btn_back_click()
        elif event == "btn_num_click":
            self.btn_num_click(msg)
            return
        elif event == "btn_symbol_click":
            self.btn_symbol_click(msg)
            return
        elif event == "btn_up_click":
            self.btn_up_click()
        elif event == "btn_down_click":
            self.btn_down_click()
        elif event == "btn_left_click":
            self.btn_left_click()
        elif event == "btn_right_click":
            self.btn_right_click()
        elif event == "btn_fast_1_click":
            self.btn_fast_click(0)
        elif event == "btn_fast_2_click":
            self.btn_fast_click(1)
        elif event == "btn_fast_3_click":
            self.btn_fast_click(2)
        elif event == "btn_fast_4_click":
            self.btn_fast_click(3)
        elif event == "btn_hands_free_click":
            self.btn_hands_free_click(msg)
        elif event == "btn_headset_click":
            self.btn_headset_click()
        elif event == "btn_redial_click":
            self.btn_redial_click()
        elif event == "btn_call_click":
            self.btn_call_click()
        elif event == "btn_pwk_click" or event == "pwk_up_handle":
            self.btn_pwk_click()
        elif event == "btn_pwk_long_click":
            self.btn_pwk_long_click()
        elif event == "btn_num_long_click":
            self.btn_num_long_click(msg)
        elif event == "btn_fast_long_click":
            self.btn_fast_long_click(msg)
        elif event == "btn_hook_click":
            self.btn_hook_click(msg)

    def btn_menu_click(self):
        self.cur_screen.btn_menu_click()

    def btn_back_click(self):
        self.cur_screen.btn_back_click()

    def btn_num_click(self, msg):
        self.cur_screen.btn_num_click(msg)

    def btn_num_long_click(self,msg):
        self.cur_screen.btn_num_long_click(msg)

    def btn_fast_long_click(self,msg):
        if self.cur_screen.NAME == "main":
            EventMesh.publish("load_screen", {"screen": "quickkey","meta_info":{"flag":"1"}})
        self.cur_screen.btn_fast_long_click(msg)

    def btn_symbol_click(self, msg):
        self.cur_screen.btn_symbol_click(msg)
        
    def btn_up_click(self):
        self.cur_screen.btn_up_click()

    def btn_down_click(self):
        self.cur_screen.btn_down_click()

    def btn_left_click(self):
        self.cur_screen.btn_left_click()

    def btn_right_click(self):
        self.cur_screen.btn_right_click()

    def btn_fast_click(self,No):
        self.language = EventMesh.publish("persistent_config_get", "language")
        if self.cur_screen.NAME == "main":
            self.key = EventMesh.publish("persistent_config_get", "quikkey")
            if self.key == None:
                self.key = ["","","",""]
            if len(self.key[No])!=0:
                name = EventMesh.publish("get_phone_number_name", self.key[No])
                print("UiControls func btn_fast_click name:",name," number:",self.key[No])
                status = sim.getStatus()
                if status == 0:
                    if not (self.key[No]=="110" or self.key[No]=="112" or self.key[No]=="119" or self.key[No]=="120" or self.key[No]=="122" or self.key[No] == "911"):
                        if self.language == "zh":
                            EventMesh.publish("window_show", "仅能拨打紧急号码")
                        else:
                            EventMesh.publish("window_show", "Only Emergency Numbers Can Be Dialed")
                        return
                EventMesh.publish("load_screen", {"screen": "call_out", "meta_info": [name,self.key[No]]})
        else:
            self.cur_screen.btn_fast_click(No)

    def btn_hands_free_click(self,msg):
        print("handsfree msg:",msg)
        self.num_len = EventMesh.publish("get_cur_number_len")
        print("num_len:",self.num_len)
        hook_status = EventMesh.publish("get_hook_state")  #获取一下当前的插簧状态
        if not self.cur_screen.btn_hands_free_click(msg) :  #如果当前界面没有定义btn_hands_free_click则进入
            if self.cur_screen.NAME == "reset":
                return
            if hook_status == 0:    #如果插簧抬起，则按免提会在话筒与免提之间切换
                if self.num_len == 0:  #若是当前无号码输入
                    print("当前音量1：",self.Audio.getVolume())
                    # EventMesh.publish("set_audio_volume", 0)
                    self.Audio.setVolume(11)
                    EventMesh.publish("audio_tone")  #响拨号音
                if self.cur_screen.NAME != "keypad_input" :     #当前屏幕非拨号界面
                    EventMesh.publish("load_screen", {"screen": "keypad_input", "meta_info": ["8",None]})
            elif hook_status == 1:  #如果插簧按下，则按免提会在免提与主界面之间切换
                if self.num_len == 0:
                    print("当前音量1：",self.Audio.getVolume())
                    # EventMesh.publish("set_audio_volume", 0)
                    self.Audio.setVolume(11)
                    EventMesh.publish("audio_tone")  #响拨号音
                if msg:
                    if self.cur_screen.NAME != "keypad_input" :     #当前屏幕非拨号界面
                        EventMesh.publish("load_screen", {"screen": "keypad_input", "meta_info": ["8",None]})
                    else:   #当前屏幕是拨号界面，此处为处理不按免提直接按号码进入拨号界面的状态
                        EventMesh.publish("audio_disable")
                        EventMesh.publish("set_handfree_state", 0)
                        EventMesh.publish("load_screen", {"screen": "main"})
                else:
                    # EventMesh.publish("set_handfree_state", 0)
                    EventMesh.publish("audio_tone_stop")
                    EventMesh.publish("load_screen", {"screen": "main"})

    def btn_headset_click(self):
        self.cur_screen.btn_headset_click()

    def btn_redial_click(self):
        self.cur_screen.btn_redial_click()

    def btn_call_click(self):
        # print("UiControls btn_call_click cur_screen.NAME:",self.cur_screen.NAME)
        self.cur_screen.btn_call_click()

    def get_current_screen_name(self, event=None, data=None):
        return self.cur_screen.NAME
    
    def btn_pwk_click(self):
        EventMesh.publish("set_contrast")
        if EventMesh.publish("get_IsgoTomain","0")==1:
            if not self.cur_screen.btn_pwk_click():
                EventMesh.publish("load_screen", {"screen": "main"})
            # else:
            #     self.cur_screen.btn_pwk_click()
        else:
            self.cur_screen.btn_pwk_click()

    def btn_pwk_long_click(self):
        if self.cur_screen.NAME == "charging":
            self.cur_screen.btn_pwk_long_click()
        else:
            EventMesh.publish("load_screen", {"screen": "welcome","meta_info":1})
            utime.sleep(2)
            EventMesh.publish("power_close")
            Power.powerDown()

    def power_close(self,topic=None, data=None):
        _lcd = LCD()
        _lcd.lcd_write_cmd(0xAE,1)
        _lcd.lcd_write_cmd(0xA5,1)
        utime.sleep_ms(500)
        gpio18 = Pin(Pin.GPIO38 , Pin.OUT, Pin.PULL_DISABLE, 0)
        print("灭屏？4")
        gpio18.write(0)
    
    def btn_hook_click(self,msg):
        EventMesh.publish("stop_update_screen")
        print("hook msg:",msg)
        if self.cur_screen.NAME == "charging":
            return
        self.num_len = EventMesh.publish("get_cur_number_len")
        print("num_len:",self.num_len)
        if not self.cur_screen.btn_hook_click(msg):
            if self.cur_screen.NAME == "AlarmClockring" :
                print("关闭闹铃")
                EventMesh.publish("close_alarm")
            if not msg:   #插簧抬起
                print("抬起")
                self.hook_click_num = 1
                # if self.cur_screen.NAME == "AlarmClockring" :
                #     print("关闭闹铃")
                #     EventMesh.publish("close_alarm")
                if self.num_len == 0:
                    # EventMesh.publish("set_audio_volume", 0)
                    self.Audio.setVolume(11)
                    EventMesh.publish("audio_tone")
                if self.cur_screen.NAME != "keypad_input" :     #当前屏幕非拨号界面
                    EventMesh.publish("load_screen", {"screen": "keypad_input", "meta_info": ["8",None]})
            else:       #插簧按下
                print("按下")
                self.hook_click_num = 0
                EventMesh.publish("audio_disable")
                EventMesh.publish("audio_tone_stop")
                EventMesh.publish("load_screen", {"screen": "main"})

    def hook_click_flag(self,topic=None, data=None):
        return self.hook_click_num
    
    def start(self):
        # 启动UI
        self.post_processor_after_instantiation()

    def finish(self):
        self.lcd_state_machine.timer_restart()

################################################################
#                   禁拨号码管理相关Screen类
################################################################

class BlockedNumbersMainScreen(Screen):
    """
    禁拨号码管理主界面
    """
    NAME = "blocked_numbers_main"

    def __init__(self):
        super().__init__()
        self.meta = blocked_numbers_main_screen
        self.cur = 0
        self.language = "zh"
        self.count = 2  # 简化为只有2个选项：添加和删除
        self.blocked_numbers_main_list = blocked_numbers_main_list
        self.blocked_numbers_main_title = blocked_numbers_main_title
        self.blocked_numbers_main_sure = blocked_numbers_main_sure
        self.blocked_numbers_main_return = blocked_numbers_main_return
        self.btn_list = []
        self.currentButton = None
        # 简化菜单：移除查看功能，只保留添加和删除
        self.blocked_numbers_menu_list = [
            {
                "screen": "blocked_numbers_add",
                "title": " 1 添加禁拨号码",
            },
            {
                "screen": "blocked_numbers_delete",
                "title": " 2 删除禁拨号码",
            },
        ]
        self.blocked_numbers_menu_list_en = [
            {
                "screen": "blocked_numbers_add",
                "title": " 1 Add Blocked Number",
            },
            {
                "screen": "blocked_numbers_delete",
                "title": " 2 Delete Blocked Number",
            },
        ]

    def initialization(self):
        self.btn_list = []
        self.list_create()
        return True

    def list_create(self):
        self.blocked_numbers_main_list.delete()
        self.blocked_numbers_main_list = lv.list(self.meta)
        self.blocked_numbers_main_list.set_pos(0, 16)
        self.blocked_numbers_main_list.set_size(128, 32)
        self.blocked_numbers_main_list.set_style_pad_left(0, 0)
        self.blocked_numbers_main_list.set_style_pad_top(0, 1)
        self.blocked_numbers_main_list.set_style_pad_row(0, 0)
        self.blocked_numbers_main_list.add_style(style_cont_black, lv.PART.MAIN | lv.STATE.DEFAULT)
        self.blocked_numbers_main_list.add_style(style_list_scrollbar, lv.PART.SCROLLBAR)

        self.language = EventMesh.publish("persistent_config_get", "language")
        if self.language == "zh":
            self.option = self.blocked_numbers_menu_list
            self.blocked_numbers_main_title.set_text("禁拨号码管理")
            self.blocked_numbers_main_sure.set_text("选择")
            self.blocked_numbers_main_return.set_text("返回")
        else:
            self.option = self.blocked_numbers_menu_list_en
            self.blocked_numbers_main_title.set_text("Blocked Numbers")
            self.blocked_numbers_main_sure.set_text("Select")
            self.blocked_numbers_main_sure.set_size(48, 12)
            self.blocked_numbers_main_return.set_pos(104, 51)
            self.blocked_numbers_main_return.set_text("Back")

        for idx, item in enumerate(self.option):
            blocked_obj_list_btn = lv.btn(self.blocked_numbers_main_list)
            blocked_obj_list_btn.set_pos(0, 1)
            blocked_obj_list_btn.set_size(115, 16)
            blocked_obj_list_btn.add_style(style_list_black, lv.PART.MAIN | lv.STATE.DEFAULT)
            blocked_obj_list_label = lv.label(blocked_obj_list_btn)
            blocked_obj_list_label.center()
            blocked_obj_list_label.set_pos(0, 2)
            blocked_obj_list_label.set_size(115, 16)
            blocked_obj_list_label.set_text(item["title"])
            blocked_obj_list_label.set_long_mode(lv.label.LONG.SCROLL_CIRCULAR)
            blocked_obj_list_label.set_style_text_align(lv.TEXT_ALIGN.LEFT, 0)
            blocked_obj_list_label.add_style(style_font_black_montserrat_10, lv.PART.MAIN | lv.STATE.DEFAULT)
            self.btn_list.append(blocked_obj_list_btn)
        self.add_state()

    def add_state(self):
        if self.currentButton:
            # 恢复未选中状态：黑色背景，白色文字
            self.currentButton.set_style_bg_color(lv.color_make(0x00, 0x00, 0x00), lv.PART.MAIN | lv.STATE.DEFAULT)
            self.currentButton.set_style_bg_grad_color(lv.color_make(0x00, 0x00, 0x00), lv.PART.MAIN | lv.STATE.DEFAULT)
            # 获取按钮内的标签并设置文字颜色
            label = self.currentButton.get_child(0)
            if label:
                label.set_style_text_color(lv.color_make(0xff, 0xff, 0xff), lv.PART.MAIN | lv.STATE.DEFAULT)

        self.currentButton = self.btn_list[self.cur]
        # 设置选中状态：白色背景，黑色文字
        self.currentButton.set_style_bg_color(lv.color_make(0xff, 0xff, 0xff), lv.PART.MAIN | lv.STATE.DEFAULT)
        self.currentButton.set_style_bg_grad_color(lv.color_make(0xff, 0xff, 0xff), lv.PART.MAIN | lv.STATE.DEFAULT)
        # 获取按钮内的标签并设置文字颜色
        label = self.currentButton.get_child(0)
        if label:
            label.set_style_text_color(lv.color_make(0x00, 0x00, 0x00), lv.PART.MAIN | lv.STATE.DEFAULT)

    def clear_state(self):
        if self.currentButton:
            # 恢复未选中状态：黑色背景，白色文字
            self.currentButton.set_style_bg_color(lv.color_make(0x00, 0x00, 0x00), lv.PART.MAIN | lv.STATE.DEFAULT)
            self.currentButton.set_style_bg_grad_color(lv.color_make(0x00, 0x00, 0x00), lv.PART.MAIN | lv.STATE.DEFAULT)
            # 获取按钮内的标签并设置文字颜色
            label = self.currentButton.get_child(0)
            if label:
                label.set_style_text_color(lv.color_make(0xff, 0xff, 0xff), lv.PART.MAIN | lv.STATE.DEFAULT)

    def next_idx(self, cur, length):
        return (cur + 1) % length

    def prev_idx(self, cur, length):
        return (cur - 1) % length

    def btn_down_click(self):
        """向下选择"""
        self.clear_state()
        self.cur = self.next_idx(self.cur, self.count)
        self.add_state()

    def btn_up_click(self):
        """向上选择"""
        self.clear_state()
        self.cur = self.prev_idx(self.cur, self.count)
        self.add_state()

    def btn_menu_click(self):
        """确认选择按键"""
        if self.cur == 0:  # 添加禁拨号码
            EventMesh.publish("load_screen", {"screen": "keypad_input", "meta_info": ["blocked_add", ""]})
        elif self.cur == 1:  # 删除禁拨号码
            EventMesh.publish("load_screen", {"screen": "keypad_input", "meta_info": ["blocked_delete", ""]})

    def btn_back_click(self):
        """返回按键"""
        if self.cur > 0:
            self.clear_state()
            self.cur = 0
        EventMesh.publish("load_screen", {"screen": "main"})

    def btn_pwk_click(self):
        """呼叫挂断返回主界面"""
        EventMesh.publish("load_screen", {"screen": "main"})
        return True


# BlockedNumbersListScreen类已移除以节省内存
# 查看禁拨号码功能已简化，用户可通过添加/删除功能管理号码
