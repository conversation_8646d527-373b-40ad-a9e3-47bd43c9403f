#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
禁拨号码设置功能修复验证脚本

测试场景：
1. 进入添加号码界面后，在未点击"Add"按钮前按数字键不应有响应
2. 点击"Add"按钮后，按数字键应该正常显示输入
3. 退格清空输入后，输入模式应该重置为非激活状态
"""

class MockEventMesh:
    """模拟EventMesh用于测试"""
    @staticmethod
    def publish(event, data=None):
        print(f"EventMesh.publish: {event}, data: {data}")
        if event == "persistent_config_get":
            if data == "language":
                return "zh"
            elif data == "blocked_numbers":
                return []
        elif event == "window_show":
            print(f"显示消息: {data}")
        return None

class MockKeypadInput:
    """模拟KeypadInput类用于测试"""
    def __init__(self, cursor_key_id):
        self.cursor_key_id = cursor_key_id
        self.input_phone_number = ""
        self.language = "zh"
        
        # 添加输入模式状态控制（用于禁拨号码管理）
        self.input_mode_active = False
        # 对于禁拨号码添加/删除界面，默认输入模式为非激活状态
        if self.cursor_key_id == "blocked_add" or self.cursor_key_id == "blocked_delete":
            self.input_mode_active = False
        else:
            # 其他界面默认激活输入模式（保持原有行为）
            self.input_mode_active = True
    
    def btn_num_click(self, number):
        """拨号"""
        print(f"按下数字键: {number}")
        
        # 检查禁拨号码管理界面的输入模式状态
        if (self.cursor_key_id == "blocked_add" or self.cursor_key_id == "blocked_delete") and not self.input_mode_active:
            # 输入模式未激活，不响应数字键输入
            if self.language == "zh":
                MockEventMesh.publish("window_show", "请先点击按钮激活输入模式")
            else:
                MockEventMesh.publish("window_show", "Please click button to activate input mode")
            return
        
        # 输入模式已激活，正常处理数字输入
        if len(self.input_phone_number) < 20:
            self.input_phone_number += number
            self.update_phone_number(None, self.input_phone_number)
    
    def btn_left_click(self):
        """左键点击（Add/Delete按钮）"""
        print(f"点击左键按钮")
        
        # 处理禁拨号码
        if self.cursor_key_id == "blocked_add":
            # 如果输入模式未激活，点击"Add"按钮激活输入模式
            if not self.input_mode_active:
                self.input_mode_active = True
                if self.language == "zh":
                    MockEventMesh.publish("window_show", "输入模式已激活，请输入号码")
                else:
                    MockEventMesh.publish("window_show", "Input mode activated, enter number")
                return
            
            # 输入模式已激活，执行添加操作
            if self.input_phone_number == "":
                MockEventMesh.publish("window_show","请输入号码" if self.language == "zh" else "Please enter number")
            else:
                print(f"添加号码: {self.input_phone_number}")
                MockEventMesh.publish("window_show","添加成功" if self.language == "zh" else "Added successfully")
        elif self.cursor_key_id == "blocked_delete":
            # 如果输入模式未激活，点击"Delete"按钮激活输入模式
            if not self.input_mode_active:
                self.input_mode_active = True
                if self.language == "zh":
                    MockEventMesh.publish("window_show", "输入模式已激活，请输入号码")
                else:
                    MockEventMesh.publish("window_show", "Input mode activated, enter number")
                return
            
            # 输入模式已激活，执行删除操作
            if self.input_phone_number == "":
                MockEventMesh.publish("window_show","请输入号码" if self.language == "zh" else "Please enter number")
            else:
                print(f"删除号码: {self.input_phone_number}")
                MockEventMesh.publish("window_show","删除成功" if self.language == "zh" else "Deleted successfully")
    
    def backspace_phone_number(self):
        """退格操作"""
        phone_number_len = len(self.input_phone_number)
        if phone_number_len == 0:
            # 如果是禁拨号码界面，重置输入模式状态
            if self.cursor_key_id == "blocked_add" or self.cursor_key_id == "blocked_delete":
                self.input_mode_active = False
                print("输入模式已重置为非激活状态")
        else:
            if phone_number_len == 1:
                self.input_phone_number = ""
                # 如果是禁拨号码界面且输入框清空，重置输入模式状态
                if (self.cursor_key_id == "blocked_add" or self.cursor_key_id == "blocked_delete"):
                    self.input_mode_active = False
                    print("输入模式已重置为非激活状态")
            else:
                self.input_phone_number = self.input_phone_number[:-1]
        self.update_phone_number(None, self.input_phone_number)
    
    def update_phone_number(self, topic, phone_num):
        """更新显示的号码"""
        print(f"显示号码: '{phone_num}', 输入模式激活: {self.input_mode_active}")

def test_blocked_add_scenario():
    """测试添加禁拨号码场景"""
    print("=" * 50)
    print("测试场景：添加禁拨号码")
    print("=" * 50)
    
    # 创建添加禁拨号码界面实例
    keypad = MockKeypadInput("blocked_add")
    
    print("\n1. 初始状态检查:")
    print(f"   输入模式激活状态: {keypad.input_mode_active}")
    print(f"   当前输入号码: '{keypad.input_phone_number}'")
    
    print("\n2. 未点击Add按钮前，按数字键测试:")
    keypad.btn_num_click("1")
    keypad.btn_num_click("3")
    keypad.btn_num_click("8")
    print(f"   当前输入号码: '{keypad.input_phone_number}' (应该为空)")
    
    print("\n3. 点击Add按钮激活输入模式:")
    keypad.btn_left_click()
    print(f"   输入模式激活状态: {keypad.input_mode_active}")
    
    print("\n4. 激活输入模式后，按数字键测试:")
    keypad.btn_num_click("1")
    keypad.btn_num_click("3")
    keypad.btn_num_click("8")
    keypad.btn_num_click("0")
    keypad.btn_num_click("0")
    print(f"   当前输入号码: '{keypad.input_phone_number}' (应该为'13800')")
    
    print("\n5. 退格清空测试:")
    for i in range(5):
        keypad.backspace_phone_number()
    print(f"   输入模式激活状态: {keypad.input_mode_active} (应该为False)")
    
    print("\n6. 清空后再次按数字键测试:")
    keypad.btn_num_click("9")
    print(f"   当前输入号码: '{keypad.input_phone_number}' (应该为空)")

def test_blocked_delete_scenario():
    """测试删除禁拨号码场景"""
    print("\n" + "=" * 50)
    print("测试场景：删除禁拨号码")
    print("=" * 50)
    
    # 创建删除禁拨号码界面实例
    keypad = MockKeypadInput("blocked_delete")
    
    print("\n1. 初始状态检查:")
    print(f"   输入模式激活状态: {keypad.input_mode_active}")
    
    print("\n2. 未点击Delete按钮前，按数字键测试:")
    keypad.btn_num_click("1")
    print(f"   当前输入号码: '{keypad.input_phone_number}' (应该为空)")
    
    print("\n3. 点击Delete按钮激活输入模式:")
    keypad.btn_left_click()
    
    print("\n4. 激活后正常输入:")
    keypad.btn_num_click("1")
    keypad.btn_num_click("2")
    keypad.btn_num_click("3")
    print(f"   当前输入号码: '{keypad.input_phone_number}' (应该为'123')")

def test_normal_interface():
    """测试普通界面（非禁拨号码界面）"""
    print("\n" + "=" * 50)
    print("测试场景：普通拨号界面（确保不影响原有功能）")
    print("=" * 50)
    
    # 创建普通拨号界面实例
    keypad = MockKeypadInput(8)  # 普通拨号界面
    
    print("\n1. 初始状态检查:")
    print(f"   输入模式激活状态: {keypad.input_mode_active} (应该为True)")
    
    print("\n2. 直接按数字键测试:")
    keypad.btn_num_click("1")
    keypad.btn_num_click("2")
    keypad.btn_num_click("3")
    print(f"   当前输入号码: '{keypad.input_phone_number}' (应该为'123')")

if __name__ == "__main__":
    print("禁拨号码设置功能修复验证测试")
    print("测试修复后的交互逻辑是否正确")
    
    test_blocked_add_scenario()
    test_blocked_delete_scenario()
    test_normal_interface()
    
    print("\n" + "=" * 50)
    print("测试完成！")
    print("如果所有测试都按预期运行，说明修复成功。")
    print("=" * 50)
